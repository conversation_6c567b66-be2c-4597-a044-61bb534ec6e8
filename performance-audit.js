// Performance and Resource Audit Script
// Run this in Chrome DevTools Console to analyze resource usage

console.log('🔍 Starting Performance and Resource Audit...\n');

// 1. Memory Usage Analysis
function analyzeMemoryUsage() {
  console.log('📊 MEMORY USAGE ANALYSIS');
  console.log('========================');
  
  if (performance.memory) {
    const memory = performance.memory;
    console.log(`Used JS Heap Size: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Total JS Heap Size: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`JS Heap Size Limit: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Memory Usage: ${((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100).toFixed(2)}%`);
  } else {
    console.log('Memory API not available in this browser');
  }
  console.log('');
}

// 2. DOM Analysis
function analyzeDOMComplexity() {
  console.log('🌳 DOM COMPLEXITY ANALYSIS');
  console.log('==========================');
  
  const totalElements = document.querySelectorAll('*').length;
  const divElements = document.querySelectorAll('div').length;
  const eventListeners = getEventListeners ? Object.keys(getEventListeners(document)).length : 'N/A';
  
  console.log(`Total DOM Elements: ${totalElements}`);
  console.log(`Div Elements: ${divElements}`);
  console.log(`Event Listeners on Document: ${eventListeners}`);
  
  // Check for potential memory leaks
  const elementsWithInlineStyles = document.querySelectorAll('[style]').length;
  console.log(`Elements with Inline Styles: ${elementsWithInlineStyles}`);
  
  // Check for large data attributes
  const elementsWithDataAttrs = document.querySelectorAll('[data-*]').length;
  console.log(`Elements with Data Attributes: ${elementsWithDataAttrs}`);
  console.log('');
}

// 3. Resource Loading Analysis
function analyzeResourceLoading() {
  console.log('📦 RESOURCE LOADING ANALYSIS');
  console.log('============================');
  
  const resources = performance.getEntriesByType('resource');
  let totalSize = 0;
  const resourceTypes = {};
  
  resources.forEach(resource => {
    const type = resource.initiatorType || 'other';
    if (!resourceTypes[type]) {
      resourceTypes[type] = { count: 0, size: 0, duration: 0 };
    }
    resourceTypes[type].count++;
    resourceTypes[type].duration += resource.duration;
    
    // Estimate size (not always available)
    if (resource.transferSize) {
      resourceTypes[type].size += resource.transferSize;
      totalSize += resource.transferSize;
    }
  });
  
  console.log(`Total Resources Loaded: ${resources.length}`);
  console.log(`Estimated Total Size: ${(totalSize / 1024).toFixed(2)} KB`);
  console.log('\nBreakdown by Resource Type:');
  
  Object.entries(resourceTypes).forEach(([type, stats]) => {
    console.log(`  ${type}: ${stats.count} files, ${(stats.size / 1024).toFixed(2)} KB, ${stats.duration.toFixed(2)}ms avg`);
  });
  console.log('');
}

// 4. Performance Metrics
function analyzePerformanceMetrics() {
  console.log('⚡ PERFORMANCE METRICS');
  console.log('=====================');
  
  const navigation = performance.getEntriesByType('navigation')[0];
  if (navigation) {
    console.log(`DOM Content Loaded: ${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`);
    console.log(`Load Event: ${navigation.loadEventEnd - navigation.loadEventStart}ms`);
    console.log(`Total Page Load: ${navigation.loadEventEnd - navigation.navigationStart}ms`);
  }
  
  // Check for long tasks
  if ('PerformanceObserver' in window) {
    const longTasks = performance.getEntriesByType('longtask');
    console.log(`Long Tasks (>50ms): ${longTasks.length}`);
    if (longTasks.length > 0) {
      console.log('⚠️  Warning: Long tasks detected that may cause jank');
    }
  }
  console.log('');
}

// 5. React-specific Analysis
function analyzeReactPerformance() {
  console.log('⚛️  REACT PERFORMANCE ANALYSIS');
  console.log('==============================');
  
  // Check for React DevTools
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('✅ React DevTools detected');
    
    // Check for React components
    const reactRoots = document.querySelectorAll('[data-reactroot]');
    console.log(`React Root Elements: ${reactRoots.length}`);
    
    // Check for potential issues
    const elementsWithKeys = document.querySelectorAll('[data-reactid]').length;
    console.log(`Elements with React IDs: ${elementsWithKeys}`);
  } else {
    console.log('❌ React DevTools not detected');
  }
  console.log('');
}

// 6. Animation and Rendering Analysis
function analyzeAnimationPerformance() {
  console.log('🎬 ANIMATION & RENDERING ANALYSIS');
  console.log('=================================');
  
  // Check for CSS animations
  const animatedElements = document.querySelectorAll('*').length;
  let cssAnimations = 0;
  let transforms = 0;
  
  document.querySelectorAll('*').forEach(el => {
    const styles = window.getComputedStyle(el);
    if (styles.animationName !== 'none') cssAnimations++;
    if (styles.transform !== 'none') transforms++;
  });
  
  console.log(`Elements with CSS Animations: ${cssAnimations}`);
  console.log(`Elements with Transforms: ${transforms}`);
  
  // Check for Framer Motion (if present)
  if (window.FramerMotion || document.querySelector('[data-framer-motion]')) {
    console.log('✅ Framer Motion detected');
  }
  console.log('');
}

// 7. Recommendations
function generateRecommendations() {
  console.log('💡 OPTIMIZATION RECOMMENDATIONS');
  console.log('===============================');
  
  const recommendations = [];
  
  // Memory recommendations
  if (performance.memory && (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) > 0.8) {
    recommendations.push('⚠️  High memory usage detected - consider optimizing component re-renders');
  }
  
  // DOM recommendations
  const totalElements = document.querySelectorAll('*').length;
  if (totalElements > 1500) {
    recommendations.push('⚠️  Large DOM detected - consider virtualizing long lists');
  }
  
  // Resource recommendations
  const resources = performance.getEntriesByType('resource');
  if (resources.length > 50) {
    recommendations.push('⚠️  Many resources loaded - consider code splitting and lazy loading');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('✅ No major performance issues detected');
  }
  
  recommendations.forEach(rec => console.log(rec));
  console.log('');
}

// 8. React-specific Performance Issues
function analyzeReactSpecificIssues() {
  console.log('⚛️  REACT-SPECIFIC PERFORMANCE ISSUES');
  console.log('====================================');

  // Check for potential re-render issues
  const elementsWithDataReactId = document.querySelectorAll('[data-reactid]').length;
  const elementsWithReactProps = document.querySelectorAll('[data-react-*]').length;

  console.log(`Elements with React IDs: ${elementsWithDataReactId}`);
  console.log(`Elements with React Props: ${elementsWithReactProps}`);

  // Check for Framer Motion elements (potential performance impact)
  const framerMotionElements = document.querySelectorAll('[data-framer-motion]').length;
  const elementsWithTransforms = Array.from(document.querySelectorAll('*')).filter(el => {
    const style = window.getComputedStyle(el);
    return style.transform !== 'none' || style.willChange !== 'auto';
  }).length;

  console.log(`Framer Motion Elements: ${framerMotionElements}`);
  console.log(`Elements with Transforms/Will-Change: ${elementsWithTransforms}`);

  // Check for potential state management issues
  const inputElements = document.querySelectorAll('input, select, textarea').length;
  console.log(`Form Elements (potential state triggers): ${inputElements}`);

  console.log('');
}

// 9. Fund-Wise Simulator Specific Analysis
function analyzeFundWiseSimulator() {
  console.log('💰 FUND-WISE SIMULATOR ANALYSIS');
  console.log('===============================');

  // Check for calculator-specific elements
  const sliders = document.querySelectorAll('[role="slider"]').length;
  const cards = document.querySelectorAll('[class*="card"]').length;
  const buttons = document.querySelectorAll('button').length;

  console.log(`Sliders: ${sliders}`);
  console.log(`Cards: ${cards}`);
  console.log(`Buttons: ${buttons}`);

  // Check for calculation-heavy elements
  const elementsWithCurrency = document.querySelectorAll('*').length;
  let currencyElements = 0;
  document.querySelectorAll('*').forEach(el => {
    if (el.textContent && el.textContent.includes('$')) {
      currencyElements++;
    }
  });

  console.log(`Elements with Currency ($): ${currencyElements}`);

  // Check for potential calculation triggers
  const rangeInputs = document.querySelectorAll('input[type="range"]').length;
  const numberInputs = document.querySelectorAll('input[type="number"]').length;

  console.log(`Range Inputs: ${rangeInputs}`);
  console.log(`Number Inputs: ${numberInputs}`);
  console.log('');
}

// Run all analyses
function runFullAudit() {
  analyzeMemoryUsage();
  analyzeDOMComplexity();
  analyzeResourceLoading();
  analyzePerformanceMetrics();
  analyzeReactPerformance();
  analyzeAnimationPerformance();
  analyzeReactSpecificIssues();
  analyzeFundWiseSimulator();
  generateRecommendations();

  console.log('🎯 AUDIT COMPLETE');
  console.log('=================');
  console.log('For more detailed analysis, use Chrome DevTools:');
  console.log('1. Open DevTools (F12)');
  console.log('2. Go to Performance tab');
  console.log('3. Record a performance profile');
  console.log('4. Check Memory tab for heap snapshots');
  console.log('5. Use Lighthouse for comprehensive audit');
  console.log('6. Monitor the Network tab for resource loading');
  console.log('7. Use React DevTools Profiler for component analysis');
}

// Auto-run the audit
runFullAudit();
