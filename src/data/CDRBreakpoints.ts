
// CDR (Commission Distribution Rate) breakpoints for Equity Funds
export const CDRBreakpoints = [
  { min: 0, max: 24999, rate: 0.0500 },        // $0 - $24,999: 5.00%
  { min: 25000, max: 49999, rate: 0.0450 },    // $25,000 - $49,999: 4.50%
  { min: 50000, max: 99999, rate: 0.0400 },    // $50,000 - $99,999: 4.00%
  { min: 100000, max: 249999, rate: 0.0300 },  // $100,000 - $249,999: 3.00%
  { min: 250000, max: 499999, rate: 0.0250 },  // $250,000 - $499,999: 2.50%
  { min: 500000, max: 749999, rate: 0.0180 },  // $500,000 - $749,999: 1.80%
  { min: 750000, max: 999999, rate: 0.0120 },  // $750,000 - $999,999: 1.20%
  { min: 1000000, max: Infinity, rate: 0.0100 } // $1,000,000+: 1.00%
];

// Helper function to get CDR rate based on amount
export const getCDRRate = (amount: number): number => {
  const breakpoint = CDRBreakpoints.find(
    bp => amount >= bp.min && amount <= bp.max
  );
  return breakpoint ? breakpoint.rate : 0;
};
