import { ScenarioSettings, FUND_TYPE_CONFIGS, REP_LEVEL_CONFIGS } from '@/types/scenarioSettings';
import { CDRBreakpoints } from '@/data/CDRBreakpoints';

/**
 * Apply scenario settings to get the appropriate CDR rate
 */
export const getAdjustedCDRRate = (amount: number, settings: ScenarioSettings): number => {
  // Get base CDR rate
  const baseBreakpoint = CDRBreakpoints.find(
    bp => amount >= bp.min && amount <= bp.max
  );
  const baseRate = baseBreakpoint ? baseBreakpoint.rate : 0;

  // Apply fund type multiplier
  const fundConfig = FUND_TYPE_CONFIGS[settings.investmentVehicle.fundType];
  const adjustedRate = baseRate * fundConfig.cdrMultiplier;

  // Apply custom CDR rates if defined
  if (settings.investmentVehicle.customCDRRates) {
    const custom = settings.investmentVehicle.customCDRRates;
    if (amount <= 24999) return custom.tier1;
    if (amount <= 49999) return custom.tier2;
    if (amount <= 99999) return custom.tier3;
    if (amount <= 249999) return custom.tier4;
    if (amount <= 499999) return custom.tier5;
    if (amount <= 749999) return custom.tier6;
    if (amount <= 999999) return custom.tier7;
    return custom.tier8;
  }

  return adjustedRate;
};

/**
 * Get commission rates based on rep level and scenario settings
 */
export const getCommissionRates = (repLevel: 'primary' | 'downline', settings: ScenarioSettings) => {
  const level = repLevel === 'primary' 
    ? settings.repLevels.primaryRepLevel 
    : settings.repLevels.downlineRepLevel;

  // Use custom rates if defined
  if (settings.repLevels.customCommissionRates?.[level]) {
    return settings.repLevels.customCommissionRates[level];
  }

  // Use default rates from config
  const config = REP_LEVEL_CONFIGS[level];
  return {
    direct: config.directCommission,
    pac: config.pacCommission,
    trail: config.trailCommission
  };
};

/**
 * Get trail rate from scenario settings
 */
export const getTrailRate = (settings: ScenarioSettings): number => {
  return settings.investmentVehicle.trailRate;
};

/**
 * Get team production ratio from scenario settings
 */
export const getTeamProductionRatio = (settings: ScenarioSettings): number => {
  return settings.productionSettings.teamProductionRatio;
};

/**
 * Get override rates from scenario settings
 */
export const getOverrideRates = (settings: ScenarioSettings) => {
  return {
    teamOverride: settings.productionSettings.overrideRates.teamOverride,
    agencyOverride: settings.productionSettings.overrideRates.agencyOverride,
    trailOverrideSpread: settings.productionSettings.trailOverrideSpread
  };
};

/**
 * Get agency start year from scenario settings
 */
export const getAgencyStartYear = (settings: ScenarioSettings): number => {
  return settings.productionSettings.agencyStartYear;
};

/**
 * Apply scenario settings to simulation inputs
 */
export const applyScenarioSettingsToInputs = (
  baseInputs: any, 
  settings: ScenarioSettings
) => {
  return {
    ...baseInputs,
    // Apply scenario defaults
    projectionYears: baseInputs.projectionYears || settings.scenarioDefaults.projectionYears,
    marketGrowth: baseInputs.marketGrowth || settings.scenarioDefaults.marketGrowthRate,
    businessOwnerAgencies: baseInputs.businessOwnerAgencies || settings.scenarioDefaults.defaultAgencies,
    businessOwnerRLsPerAgency: baseInputs.businessOwnerRLsPerAgency || settings.scenarioDefaults.defaultRLsPerAgency,
    branchOfficeAgencies: baseInputs.branchOfficeAgencies || settings.scenarioDefaults.defaultAgencies,
    branchOfficeRLsPerAgency: baseInputs.branchOfficeRLsPerAgency || settings.scenarioDefaults.defaultRLsPerAgency,
    
    // Add scenario settings for calculations
    trailRate: getTrailRate(settings),
    scenarioSettings: settings
  };
};

/**
 * Get fund type display information
 */
export const getFundTypeInfo = (settings: ScenarioSettings) => {
  return FUND_TYPE_CONFIGS[settings.investmentVehicle.fundType];
};

/**
 * Get rep level display information
 */
export const getRepLevelInfo = (repLevel: 'primary' | 'downline', settings: ScenarioSettings) => {
  const level = repLevel === 'primary' 
    ? settings.repLevels.primaryRepLevel 
    : settings.repLevels.downlineRepLevel;
  
  return REP_LEVEL_CONFIGS[level];
};

/**
 * Calculate override rate between two rep levels
 */
export const calculateOverrideRate = (
  higherLevel: keyof typeof REP_LEVEL_CONFIGS,
  lowerLevel: keyof typeof REP_LEVEL_CONFIGS,
  incomeType: 'direct' | 'pac' | 'trail' = 'direct'
): number => {
  const higher = REP_LEVEL_CONFIGS[higherLevel];
  const lower = REP_LEVEL_CONFIGS[lowerLevel];

  switch (incomeType) {
    case 'direct':
      return higher.directCommission - lower.directCommission;
    case 'pac':
      return higher.pacCommission - lower.pacCommission;
    case 'trail':
      return higher.trailCommission - lower.trailCommission;
    default:
      return higher.directCommission - lower.directCommission;
  }
};

/**
 * Validate scenario settings
 */
export const validateScenarioSettings = (settings: ScenarioSettings): string[] => {
  const errors: string[] = [];

  // Validate trail rate
  if (settings.investmentVehicle.trailRate < 0 || settings.investmentVehicle.trailRate > 0.1) {
    errors.push('Trail rate must be between 0% and 10%');
  }

  // Validate production ratio
  if (settings.productionSettings.teamProductionRatio < 0 || settings.productionSettings.teamProductionRatio > 1) {
    errors.push('Team production ratio must be between 0% and 100%');
  }

  // Validate override rates
  if (settings.productionSettings.overrideRates.teamOverride < 0 || settings.productionSettings.overrideRates.teamOverride > 0.5) {
    errors.push('Team override rate must be between 0% and 50%');
  }

  if (settings.productionSettings.overrideRates.agencyOverride < 0 || settings.productionSettings.overrideRates.agencyOverride > 0.5) {
    errors.push('Agency override rate must be between 0% and 50%');
  }

  // Validate years
  if (settings.productionSettings.agencyStartYear < 1 || settings.productionSettings.agencyStartYear > 20) {
    errors.push('Agency start year must be between 1 and 20');
  }

  if (settings.scenarioDefaults.projectionYears < 1 || settings.scenarioDefaults.projectionYears > 50) {
    errors.push('Projection years must be between 1 and 50');
  }

  return errors;
};
