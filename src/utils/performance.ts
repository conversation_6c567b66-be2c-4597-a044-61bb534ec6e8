// Performance monitoring utilities

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = process.env.NODE_ENV === 'development';

  /**
   * Start timing a performance metric
   */
  start(name: string): void {
    if (!this.isEnabled) return;

    this.metrics.set(name, {
      name,
      startTime: performance.now()
    });
  }

  /**
   * End timing a performance metric and log the result
   */
  end(name: string): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" was not started`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    // Log performance in development
    if (duration > 100) {
      console.warn(`⚠️ Slow operation: ${name} took ${duration.toFixed(2)}ms`);
    } else if (duration > 50) {
      console.log(`⏱️ ${name} took ${duration.toFixed(2)}ms`);
    }

    return duration;
  }

  /**
   * Measure a function execution time
   */
  measure<T>(name: string, fn: () => T): T {
    if (!this.isEnabled) return fn();

    this.start(name);
    try {
      const result = fn();
      this.end(name);
      return result;
    } catch (error) {
      this.end(name);
      throw error;
    }
  }

  /**
   * Measure an async function execution time
   */
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    if (!this.isEnabled) return fn();

    this.start(name);
    try {
      const result = await fn();
      this.end(name);
      return result;
    } catch (error) {
      this.end(name);
      throw error;
    }
  }

  /**
   * Get all recorded metrics
   */
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values()).filter(m => m.duration !== undefined);
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
  }

  /**
   * Get performance summary
   */
  getSummary(): { total: number; average: number; slowest: PerformanceMetric | null } {
    const completedMetrics = this.getMetrics();
    const total = completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);
    const average = completedMetrics.length > 0 ? total / completedMetrics.length : 0;
    const slowest = completedMetrics.reduce((slowest, current) => 
      !slowest || (current.duration || 0) > (slowest.duration || 0) ? current : slowest
    , null as PerformanceMetric | null);

    return { total, average, slowest };
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Convenience functions
export const startTimer = (name: string) => performanceMonitor.start(name);
export const endTimer = (name: string) => performanceMonitor.end(name);
export const measurePerformance = <T>(name: string, fn: () => T) => performanceMonitor.measure(name, fn);
export const measureAsyncPerformance = <T>(name: string, fn: () => Promise<T>) => performanceMonitor.measureAsync(name, fn);

// React hook for performance monitoring
import { useEffect } from 'react';

export const usePerformanceMonitor = (componentName: string) => {
  useEffect(() => {
    startTimer(`${componentName}-mount`);
    return () => {
      endTimer(`${componentName}-mount`);
    };
  }, [componentName]);

  return {
    startTimer: (operation: string) => startTimer(`${componentName}-${operation}`),
    endTimer: (operation: string) => endTimer(`${componentName}-${operation}`),
    measure: <T>(operation: string, fn: () => T) => measurePerformance(`${componentName}-${operation}`, fn)
  };
};
