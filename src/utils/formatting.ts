/**
 * Format a number as currency
 * @param value Number to format
 * @param currency Currency code (default: USD)
 * @param locale Locale for formatting (default: en-US)
 * @param decimals Number of decimal places (default: 0)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number,
  currency: string = 'USD',
  locale: string = 'en-US',
  decimals: number = 0
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value);
};

/**
 * Format a number as a percentage
 * @param value Number to format (e.g., 0.1 for 10%)
 * @param locale Locale for formatting (default: en-US)
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  value: number,
  locale: string = 'en-US',
  decimals: number = 2
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value);
};

/**
 * Format a number with commas
 * @param value Number to format
 * @param locale Locale for formatting (default: en-US)
 * @returns Formatted number string
 */
export const formatNumber = (
  value: number,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale).format(value);
};

/**
 * Truncate text with ellipsis
 * @param text Text to truncate
 * @param maxLength Maximum length before truncation
 * @returns Truncated text
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.slice(0, maxLength)}...`;
};

/**
 * Format a number with commas for input display (no currency symbol)
 * @param value Number to format
 * @param locale Locale for formatting (default: en-US)
 * @returns Formatted number string with commas
 */
export const formatNumberWithCommas = (
  value: number,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

/**
 * Parse a formatted number string (with commas) back to a number
 * @param value Formatted string to parse
 * @returns Parsed number or NaN if invalid
 */
export const parseFormattedNumber = (value: string): number => {
  // Handle empty string
  if (value === '') {
    return 0;
  }

  // Remove commas and check if the result is a valid number
  const cleanValue = value.replace(/,/g, '');

  // Check if the clean value contains only digits (and optional decimal point)
  if (!/^\d*\.?\d*$/.test(cleanValue)) {
    return NaN;
  }

  return parseFloat(cleanValue);
};

/**
 * Format a date
 * @param date Date to format
 * @param format Format style (default: 'medium')
 * @param locale Locale for formatting (default: en-US)
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date,
  format: 'short' | 'medium' | 'long' = 'medium',
  locale: string = 'en-US'
): string => {
  return new Intl.DateTimeFormat(locale, {
    dateStyle: format
  }).format(date);
};
