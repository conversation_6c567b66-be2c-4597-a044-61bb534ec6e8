
import { getCDRRate } from "../data/CDRBreakpoints";
import { fundTypes } from "../simConfig/fundTypes";
import { trailPercentages } from "../simConfig/trailPercentages";
import { repInfo, baseShopCommissionRates, trailRates, overrideGenerations, generationalTrailRates } from "../simConfig/repInfo";
import { trailsLogic } from "../simConfig/trailsLogic";
import { formatCurrency, formatPercentage } from "./formatting";

interface CalculationInputs {
  productType: string;
  numCases: number;
  initialInvestment: number;
  monthlyContribution: number;
  durationYears: number;
  repBP: number;
  uplineBP: number;
  viewMode?: 'agent' | 'broker';
  fundType?: 'equity' | 'income' | 'other';
  marketGrowth?: number;
  rolloverCases?: number;
  monthlyCases?: number;
  enableMultiGen?: boolean;
  genOneAgents?: number;
  genTwoAgents?: number;
  genThreeAgents?: number;
  genOneRolloverCases?: number;
  genTwoRolloverCases?: number;
  genThreeRolloverCases?: number;
  genOneMonthlyCases?: number;
  genTwoMonthlyCases?: number;
  genThreeMonthlyCases?: number;
}

interface TrailProjection {
  year: number;
  aum: number;
  personalTrail: number;
  overrideTrail: number;
  totalTrail: number;
  monthlyTrail?: number;
  newAum?: number;
  generationOverrides?: {
    gen1: number;
    gen2: number;
    gen3: number;
    total: number;
  };
}

interface GenerationCommission {
  gen1: number;
  gen2: number;
  gen3: number;
  total: number;
}

interface CalculationResults {
  totalInitialInvestment: number;
  totalContributions: number;
  totalInvested: number;
  cdrRate: number;
  cdrValue: number;
  personalCommission: number;
  overrideCommission: number;
  totalCommission: number;
  rolling12MonthAvg: number;
  trailProjections: TrailProjection[];
  generationCommissions?: GenerationCommission;
}

export const calculateCommissions = (inputs: CalculationInputs): CalculationResults => {
  // Default values for new parameters
  const viewMode = inputs.viewMode || 'agent';
  const fundType = inputs.fundType || 'equity';
  const marketGrowth = inputs.marketGrowth || 0.08;
  const rolloverCases = inputs.rolloverCases || inputs.numCases;
  const monthlyCases = inputs.monthlyCases || 0;

  // Multi-generation override parameters
  const enableMultiGen = inputs.enableMultiGen || false;
  const genOneAgents = inputs.genOneAgents || 0;
  const genTwoAgents = inputs.genTwoAgents || 0;
  const genThreeAgents = inputs.genThreeAgents || 0;
  const genOneRolloverCases = inputs.genOneRolloverCases || 0;
  const genTwoRolloverCases = inputs.genTwoRolloverCases || 0;
  const genThreeRolloverCases = inputs.genThreeRolloverCases || 0;
  const genOneMonthlyCases = inputs.genOneMonthlyCases || 0;
  const genTwoMonthlyCases = inputs.genTwoMonthlyCases || 0;
  const genThreeMonthlyCases = inputs.genThreeMonthlyCases || 0;

  // Use the correct BP rates from repInfo
  const repBP = viewMode === 'agent' ? repInfo.agent.bp : repInfo.agent.bp;
  const uplineBP = viewMode === 'agent' ? repInfo.agent.bp : repInfo.broker.bp;

  // 1. Calculate Total Initial Investment (primarily from rollovers)
  const totalInitialInvestment = rolloverCases * inputs.initialInvestment;

  // 2. Calculate Total Contributions (from monthly contribution cases)
  const totalContributions = inputs.monthlyContribution * 12 * inputs.durationYears * monthlyCases;

  // 3. Calculate Total Invested
  const totalInvested = totalInitialInvestment + totalContributions;

  // 4. Lookup CDR % based on Total Invested
  let cdrRate = getCDRRate(totalInvested);

  // Apply cap based on fund type
  const cdrCap = fundTypes[fundType].cdrCap;
  cdrRate = Math.min(cdrRate, cdrCap);

  const cdrValue = totalInvested * cdrRate;

  // 5. Calculate Personal Commission using correct BP rate
  const personalCommission = cdrValue * repBP;

  // 6. Calculate Override Commission
  // Only calculate override if upline BP is greater than rep BP
  const overrideCommission =
    viewMode === 'broker' ?
    cdrValue * (uplineBP - repBP) :
    0;

  // 7. Calculate Total Commission
  const totalCommission = personalCommission + overrideCommission;

  // 8. Calculate Rolling 12-Month Average
  const rolling12MonthAvg = totalCommission / 12;

  // 9. Calculate Multi-Generation Override Commissions (if enabled)
  let generationCommissions: GenerationCommission | undefined;

  if (viewMode === 'broker' && enableMultiGen) {
    // Calculate generation CDRs
    const gen1Invested = genOneAgents * genOneRolloverCases * inputs.initialInvestment +
                        genOneAgents * genOneMonthlyCases * inputs.monthlyContribution * 12 * inputs.durationYears;
    const gen2Invested = genTwoAgents * genTwoRolloverCases * inputs.initialInvestment +
                        genTwoAgents * genTwoMonthlyCases * inputs.monthlyContribution * 12 * inputs.durationYears;
    const gen3Invested = genThreeAgents * genThreeRolloverCases * inputs.initialInvestment +
                        genThreeAgents * genThreeMonthlyCases * inputs.monthlyContribution * 12 * inputs.durationYears;

    // Apply CDR rates to each generation's investments
    const gen1CDR = Math.min(getCDRRate(gen1Invested), cdrCap);
    const gen2CDR = Math.min(getCDRRate(gen2Invested), cdrCap);
    const gen3CDR = Math.min(getCDRRate(gen3Invested), cdrCap);

    // Apply override percentages from config
    const gen1Commission = gen1Invested * gen1CDR * overrideGenerations[1];
    const gen2Commission = gen2Invested * gen2CDR * overrideGenerations[2];
    const gen3Commission = gen3Invested * gen3CDR * overrideGenerations[3];

    generationCommissions = {
      gen1: gen1Commission,
      gen2: gen2Commission,
      gen3: gen3Commission,
      total: gen1Commission + gen2Commission + gen3Commission
    };
  }

  // 10. Calculate Trail Projections
  const trailRate = trailPercentages[fundType];
  const trailProjections: TrailProjection[] = [];

  // Yearly cohorts of AUM to track stacking monthly contributors
  const yearlyAumCohorts: number[] = [];
  let cumulativeAUM = totalInitialInvestment;

  // Get the correct trail rates based on role
  const personalTrailRate = viewMode === 'agent' ? repInfo.agent.trailRate : repInfo.agent.trailRate;
  const overrideTrailRate = viewMode === 'broker' ? repInfo.broker.trailRate - repInfo.agent.trailRate : 0;

  // Generation override trails
  const genTrails = {
    gen1Rate: generationalTrailRates[1],
    gen2Rate: generationalTrailRates[2],
    gen3Rate: generationalTrailRates[3]
  };

  // First year additions
  yearlyAumCohorts.push(monthlyCases * inputs.monthlyContribution * 12);

  for (let year = 1; year <= inputs.durationYears; year++) {
    // Grow existing initial investment AUM by market growth rate
    cumulativeAUM = cumulativeAUM * (1 + marketGrowth);

    // Grow each cohort of monthly contribution AUM
    for (let i = 0; i < yearlyAumCohorts.length; i++) {
      yearlyAumCohorts[i] = yearlyAumCohorts[i] * (1 + marketGrowth);
    }

    // Sum up all cohorts for total AUM
    const cohortSum = yearlyAumCohorts.reduce((acc, val) => acc + val, 0);
    const totalAUM = cumulativeAUM + cohortSum;

    // Add new monthly contributions cohort for next year (except for last year)
    if (year < inputs.durationYears) {
      yearlyAumCohorts.push(monthlyCases * inputs.monthlyContribution * 12);
    }

    // Calculate trails based on AUM
    const personalTrail = trailsLogic.calculateAnnualTrail({
      aum: totalAUM,
      trailRate: trailRate * personalTrailRate
    });

    // Calculate override trail if in broker mode
    const overrideTrail = viewMode === 'broker' ?
      trailsLogic.calculateAnnualTrail({
        aum: totalAUM,
        trailRate: trailRate * overrideTrailRate
      }) : 0;

    // Calculate generation override trails if enabled
    let generationOverrides;
    if (viewMode === 'broker' && enableMultiGen) {
      // Calculate AUM for each generation (simplified model)
      const gen1AUM = genOneAgents * (
        genOneRolloverCases * inputs.initialInvestment * Math.pow(1 + marketGrowth, year) +
        genOneMonthlyCases * inputs.monthlyContribution * 12 * year * Math.pow(1 + marketGrowth, year/2)
      );

      const gen2AUM = genTwoAgents * (
        genTwoRolloverCases * inputs.initialInvestment * Math.pow(1 + marketGrowth, year) +
        genTwoMonthlyCases * inputs.monthlyContribution * 12 * year * Math.pow(1 + marketGrowth, year/2)
      );

      const gen3AUM = genThreeAgents * (
        genThreeRolloverCases * inputs.initialInvestment * Math.pow(1 + marketGrowth, year) +
        genThreeMonthlyCases * inputs.monthlyContribution * 12 * year * Math.pow(1 + marketGrowth, year/2)
      );

      // Calculate trail income from each generation using generation trail rates
      const gen1Trail = gen1AUM * trailRate * genTrails.gen1Rate;
      const gen2Trail = gen2AUM * trailRate * genTrails.gen2Rate;
      const gen3Trail = gen3AUM * trailRate * genTrails.gen3Rate;

      generationOverrides = {
        gen1: gen1Trail,
        gen2: gen2Trail,
        gen3: gen3Trail,
        total: gen1Trail + gen2Trail + gen3Trail
      };
    }

    const totalTrail = personalTrail + overrideTrail + (generationOverrides?.total || 0);

    trailProjections.push({
      year,
      aum: totalAUM,
      personalTrail,
      overrideTrail,
      totalTrail,
      monthlyTrail: totalTrail / 12,
      newAum: year < inputs.durationYears ? monthlyCases * inputs.monthlyContribution * 12 : 0,
      generationOverrides
    });
  }

  return {
    totalInitialInvestment,
    totalContributions,
    totalInvested,
    cdrRate,
    cdrValue,
    personalCommission,
    overrideCommission,
    totalCommission,
    rolling12MonthAvg,
    trailProjections,
    generationCommissions
  };
};
