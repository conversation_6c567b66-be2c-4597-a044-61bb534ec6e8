import { SimpleSimulationInputs } from '@/types/simpleTypes';

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Validates simulator inputs for business rules and constraints
 */
export const validateSimulatorInputs = (inputs: SimpleSimulationInputs): ValidationResult => {
  const errors: ValidationError[] = [];

  // Validate initial investment
  if (inputs.initialInvestment <= 0) {
    errors.push({
      field: 'initialInvestment',
      message: 'Initial investment must be greater than $0'
    });
  }
  if (inputs.initialInvestment > 1000000) {
    errors.push({
      field: 'initialInvestment',
      message: 'Initial investment cannot exceed $1,000,000'
    });
  }

  // Validate rollover cases
  if (inputs.numRolloverCases < 0) {
    errors.push({
      field: 'numRolloverCases',
      message: 'Number of rollover cases cannot be negative'
    });
  }
  if (inputs.numRolloverCases > 100) {
    errors.push({
      field: 'numRolloverCases',
      message: 'Number of rollover cases cannot exceed 100'
    });
  }

  // Validate PAC per case
  if (inputs.pacPerCase < 0) {
    errors.push({
      field: 'pacPerCase',
      message: 'PAC per case cannot be negative'
    });
  }
  if (inputs.pacPerCase > 10000) {
    errors.push({
      field: 'pacPerCase',
      message: 'PAC per case cannot exceed $10,000'
    });
  }

  // Validate PAC cases
  if (inputs.numPacCases < 0) {
    errors.push({
      field: 'numPacCases',
      message: 'Number of PAC cases cannot be negative'
    });
  }
  if (inputs.numPacCases > 100) {
    errors.push({
      field: 'numPacCases',
      message: 'Number of PAC cases cannot exceed 100'
    });
  }

  // Validate projection years
  if (inputs.projectionYears < 1) {
    errors.push({
      field: 'projectionYears',
      message: 'Projection years must be at least 1'
    });
  }
  if (inputs.projectionYears > 50) {
    errors.push({
      field: 'projectionYears',
      message: 'Projection years cannot exceed 50'
    });
  }

  // Validate market growth
  if (inputs.marketGrowth < -0.5) {
    errors.push({
      field: 'marketGrowth',
      message: 'Market growth cannot be less than -50%'
    });
  }
  if (inputs.marketGrowth > 1.0) {
    errors.push({
      field: 'marketGrowth',
      message: 'Market growth cannot exceed 100%'
    });
  }

  // Validate team RLs
  if (inputs.numRLs < 0) {
    errors.push({
      field: 'numRLs',
      message: 'Number of RLs cannot be negative'
    });
  }
  if (inputs.numRLs > 50) {
    errors.push({
      field: 'numRLs',
      message: 'Number of RLs cannot exceed 50'
    });
  }

  // Validate Business Owner agencies
  if (inputs.businessOwnerAgencies < 0) {
    errors.push({
      field: 'businessOwnerAgencies',
      message: 'Business Owner agencies cannot be negative'
    });
  }
  if (inputs.businessOwnerAgencies > 20) {
    errors.push({
      field: 'businessOwnerAgencies',
      message: 'Business Owner agencies cannot exceed 20'
    });
  }

  // Validate Business Owner RLs per agency
  if (inputs.businessOwnerRLsPerAgency < 0) {
    errors.push({
      field: 'businessOwnerRLsPerAgency',
      message: 'Business Owner RLs per agency cannot be negative'
    });
  }
  if (inputs.businessOwnerRLsPerAgency > 20) {
    errors.push({
      field: 'businessOwnerRLsPerAgency',
      message: 'Business Owner RLs per agency cannot exceed 20'
    });
  }

  // Validate Branch Office agencies
  if (inputs.branchOfficeAgencies < 0) {
    errors.push({
      field: 'branchOfficeAgencies',
      message: 'Branch Office agencies cannot be negative'
    });
  }
  if (inputs.branchOfficeAgencies > 20) {
    errors.push({
      field: 'branchOfficeAgencies',
      message: 'Branch Office agencies cannot exceed 20'
    });
  }

  // Validate Branch Office RLs per agency
  if (inputs.branchOfficeRLsPerAgency < 0) {
    errors.push({
      field: 'branchOfficeRLsPerAgency',
      message: 'Branch Office RLs per agency cannot be negative'
    });
  }
  if (inputs.branchOfficeRLsPerAgency > 20) {
    errors.push({
      field: 'branchOfficeRLsPerAgency',
      message: 'Branch Office RLs per agency cannot exceed 20'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitizes input values to ensure they're within valid ranges
 */
export const sanitizeInputValue = (value: number, min: number, max: number): number => {
  if (isNaN(value)) return min;
  return Math.max(min, Math.min(max, value));
};

/**
 * Formats validation errors for display
 */
export const formatValidationErrors = (errors: ValidationError[]): string => {
  if (errors.length === 0) return '';
  if (errors.length === 1) return errors[0].message;
  return `Multiple errors: ${errors.map(e => e.message).join(', ')}`;
};
