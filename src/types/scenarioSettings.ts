// Advanced scenario settings that control all calculations
export interface ScenarioSettings {
  // Investment Vehicle Settings
  investmentVehicle: {
    fundType: 'equity' | 'fixedIncome' | 'balanced' | 'alternative';
    customCDRRates?: {
      tier1: number; // $0-$24,999
      tier2: number; // $25,000-$49,999
      tier3: number; // $50,000-$99,999
      tier4: number; // $100,000-$249,999
      tier5: number; // $250,000-$499,999
      tier6: number; // $500,000-$749,999
      tier7: number; // $750,000-$999,999
      tier8: number; // $1,000,000+
    };
    trailRate: number; // Annual trail rate (default 0.25%)
  };

  // Rep Level Configuration
  repLevels: {
    primaryRepLevel: 'RL' | 'RVP' | 'SVP' | 'EVP';
    downlineRepLevel: 'RL' | 'RVP';
    customCommissionRates?: {
      RL: {
        direct: number;
        pac: number;
        trail: number;
      };
      RVP: {
        direct: number;
        pac: number;
        trail: number;
      };
      SVP: {
        direct: number;
        pac: number;
        trail: number;
      };
      EVP: {
        direct: number;
        pac: number;
        trail: number;
      };
    };
  };

  // Override and Production Settings
  productionSettings: {
    teamProductionRatio: number; // Default 0.5 (50%)
    agencyStartYear: number; // Default 5
    overrideRates: {
      teamOverride: number; // Default 0.195 (19.5%)
      agencyOverride: number; // Default 0.15 (15%)
    };
    trailOverrideSpread: number; // Default 0.19 (19%)
  };

  // Scenario Defaults
  scenarioDefaults: {
    projectionYears: number;
    marketGrowthRate: number;
    defaultAgencies: number;
    defaultRLsPerAgency: number;
  };
}

// Default scenario settings
export const DEFAULT_SCENARIO_SETTINGS: ScenarioSettings = {
  investmentVehicle: {
    fundType: 'equity',
    trailRate: 0.0025 // 0.25%
  },
  repLevels: {
    primaryRepLevel: 'RL',
    downlineRepLevel: 'RL'
  },
  productionSettings: {
    teamProductionRatio: 0.5,
    agencyStartYear: 5,
    overrideRates: {
      teamOverride: 0.195,
      agencyOverride: 0.15
    },
    trailOverrideSpread: 0.19
  },
  scenarioDefaults: {
    projectionYears: 10,
    marketGrowthRate: 0.10,
    defaultAgencies: 2,
    defaultRLsPerAgency: 3
  }
};

// Fund type configurations
export const FUND_TYPE_CONFIGS = {
  equity: {
    name: 'Equity Funds',
    description: 'Growth-focused equity mutual funds',
    defaultTrailRate: 0.0025,
    cdrMultiplier: 1.0
  },
  fixedIncome: {
    name: 'Fixed Income Funds',
    description: 'Bond and fixed income funds',
    defaultTrailRate: 0.0020,
    cdrMultiplier: 0.8
  },
  balanced: {
    name: 'Balanced Funds',
    description: 'Mixed equity and fixed income',
    defaultTrailRate: 0.0023,
    cdrMultiplier: 0.9
  },
  alternative: {
    name: 'Alternative Investments',
    description: 'REITs, commodities, and alternatives',
    defaultTrailRate: 0.0030,
    cdrMultiplier: 1.2
  }
} as const;

// Rep level configurations
export const REP_LEVEL_CONFIGS = {
  RL: {
    name: 'Licensed Rep',
    directCommission: 0.425, // 42.5%
    pacCommission: 0.425,
    trailCommission: 0.38 // 38%
  },
  RVP: {
    name: 'Regional Vice President',
    directCommission: 0.62, // 62%
    pacCommission: 0.62,
    trailCommission: 0.57 // 57%
  },
  SVP: {
    name: 'Senior Vice President',
    directCommission: 0.70, // 70%
    pacCommission: 0.70,
    trailCommission: 0.65 // 65%
  },
  EVP: {
    name: 'Executive Vice President',
    directCommission: 0.80, // 80%
    pacCommission: 0.80,
    trailCommission: 0.75 // 75%
  }
} as const;
