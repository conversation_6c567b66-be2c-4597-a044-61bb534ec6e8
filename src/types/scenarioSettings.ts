// Advanced scenario settings that control all calculations
export interface ScenarioSettings {
  // Display Mode
  displayMode: 'public' | 'company';

  // Investment Vehicle Settings
  investmentVehicle: {
    // Investment mix percentages (must total 100%)
    investmentMix: {
      equity: number; // Percentage allocation to equity funds
      fixedIncome: number; // Percentage allocation to fixed income
      municipalBonds: number; // Percentage allocation to municipal bonds
    };
    // Share class distribution within each fund type
    shareClassMix: {
      classA: number; // Percentage in Class A shares
      classB: number; // Percentage in Class B shares
      classC: number; // Percentage in Class C shares
    };
    trailRate: number; // Annual trail rate (default 0.25%)
  };

  // Rep Level Configuration
  repLevels: {
    // Self Employed card rep level (REP through PRL)
    selfEmployedLevel: 'REP' | 'SRP' | 'DIS' | 'DIV' | 'REG' | 'SRL' | 'PRL';

    // Business Owner card (always RVP, but configurable base shop)
    businessOwnerLevel: 'RVP';
    baseShopComposition: {
      REP: number; // Number of Representatives
      SRP: number; // Number of Senior Representatives
      DIS: number; // Number of District Leaders
      DIV: number; // Number of Division Leaders
      REG: number; // Number of Regional Leaders
      SRL: number; // Number of Senior Regional Leaders
      PRL: number; // Number of Principal Regional Leaders
    };

    // Expansion Offices configuration
    expansionOffices: {
      firstGeneration: {
        numOffices: number;
        avgOfficeSize: number; // Average number of reps per office
        avgRepLevel: 'REG' | 'SRL' | 'PRL' | 'RVP'; // Average level of office leaders
      };
      secondGeneration: {
        numOffices: number;
        avgOfficeSize: number;
        avgRepLevel: 'REG' | 'SRL' | 'PRL' | 'RVP';
      };
      thirdGeneration: {
        numOffices: number;
        avgOfficeSize: number;
        avgRepLevel: 'REG' | 'SRL' | 'PRL' | 'RVP';
      };
    };
  };

  // Override and Production Settings
  productionSettings: {
    teamProductionRatio: number; // Default 0.5 (50%)
    agencyStartYear: number; // Default 5
    overrideRates: {
      teamOverride: number; // Default 0.195 (19.5%)
      agencyOverride: number; // Default 0.15 (15%)
    };
    trailOverrideSpread: number; // Default 0.19 (19%)
  };

  // Scenario Defaults
  scenarioDefaults: {
    projectionYears: number;
    marketGrowthRate: number;
    defaultAgencies: number;
    defaultRLsPerAgency: number;
  };
}

// Default scenario settings
export const DEFAULT_SCENARIO_SETTINGS: ScenarioSettings = {
  displayMode: 'public',

  investmentVehicle: {
    investmentMix: {
      equity: 60, // 60% equity funds
      fixedIncome: 25, // 25% fixed income
      municipalBonds: 15 // 15% municipal bonds
    },
    shareClassMix: {
      classA: 80, // 80% Class A shares
      classB: 15, // 15% Class B shares
      classC: 5   // 5% Class C shares
    },
    trailRate: 0.0025 // 0.25%
  },

  repLevels: {
    selfEmployedLevel: 'REG', // Default to Regional Leader
    businessOwnerLevel: 'RVP',
    baseShopComposition: {
      REP: 2, // 2 Representatives
      SRP: 1, // 1 Senior Representative
      DIS: 1, // 1 District Leader
      DIV: 1, // 1 Division Leader
      REG: 1, // 1 Regional Leader
      SRL: 0, // 0 Senior Regional Leaders
      PRL: 0  // 0 Principal Regional Leaders
    },
    expansionOffices: {
      firstGeneration: {
        numOffices: 2,
        avgOfficeSize: 5,
        avgRepLevel: 'REG'
      },
      secondGeneration: {
        numOffices: 1,
        avgOfficeSize: 3,
        avgRepLevel: 'REG'
      },
      thirdGeneration: {
        numOffices: 0,
        avgOfficeSize: 0,
        avgRepLevel: 'REG'
      }
    }
  },

  productionSettings: {
    teamProductionRatio: 0.5,
    agencyStartYear: 5,
    overrideRates: {
      teamOverride: 0.195,
      agencyOverride: 0.15
    },
    trailOverrideSpread: 0.19
  },

  scenarioDefaults: {
    projectionYears: 10,
    marketGrowthRate: 0.10,
    defaultAgencies: 2,
    defaultRLsPerAgency: 3
  }
};

// Fund type configurations with Primerica CDR structures
export const FUND_TYPE_CONFIGS = {
  equity: {
    name: 'Equity Funds',
    description: 'Class A equity mutual funds',
    cdrBreakpoints: [
      { min: 0, max: 24999, rate: 0.0500 },
      { min: 25000, max: 49999, rate: 0.0450 },
      { min: 50000, max: 99999, rate: 0.0400 },
      { min: 100000, max: 249999, rate: 0.0300 },
      { min: 250000, max: 499999, rate: 0.0250 },
      { min: 500000, max: 749999, rate: 0.0180 },
      { min: 750000, max: 999999, rate: 0.0120 },
      { min: 1000000, max: Infinity, rate: 0.0100 }
    ]
  },
  fixedIncome: {
    name: 'Fixed Income Funds',
    description: 'Taxable fixed income funds',
    cdrBreakpoints: [
      { min: 0, max: 24999, rate: 0.0400 },
      { min: 25000, max: 49999, rate: 0.0400 },
      { min: 50000, max: 99999, rate: 0.0350 },
      { min: 100000, max: 249999, rate: 0.0300 },
      { min: 250000, max: 499999, rate: 0.0200 },
      { min: 500000, max: 749999, rate: 0.0175 },
      { min: 750000, max: 999999, rate: 0.0120 },
      { min: 1000000, max: Infinity, rate: 0.0100 }
    ]
  },
  municipalBonds: {
    name: 'Municipal Bond Funds',
    description: 'Tax-exempt municipal bond funds',
    cdrBreakpoints: [
      { min: 0, max: 24999, rate: 0.0400 },
      { min: 25000, max: 49999, rate: 0.0400 },
      { min: 50000, max: 99999, rate: 0.0350 },
      { min: 100000, max: 249999, rate: 0.0300 },
      { min: 250000, max: 499999, rate: 0.0200 },
      { min: 500000, max: 749999, rate: 0.0175 },
      { min: 750000, max: 999999, rate: 0.0120 },
      { min: 1000000, max: Infinity, rate: 0.0100 }
    ]
  }
} as const;

// Share class configurations
export const SHARE_CLASS_CONFIGS = {
  classA: {
    name: 'Class A Shares',
    description: 'Front-end load shares with breakpoints',
    cdrCalculation: 'breakpoint' // Uses fund type breakpoints
  },
  classB: {
    name: 'Class B Shares',
    description: 'Back-end load shares',
    cdrRate: 0.0400 // Flat 4% rate
  },
  classC: {
    name: 'Class C Shares',
    description: 'Level load shares',
    cdrRate: 0.0100 // Flat 1% rate (estimated)
  }
} as const;

// Primerica rep level configurations (from commission schedule)
export const REP_LEVEL_CONFIGS = {
  REP: {
    code: '01',
    name: 'Representative',
    directCommission: 0.30, // 30.00%
    pacCommission: 0.30,
    trailCommission: 0.12 // 12.00%
  },
  SRP: {
    code: '05',
    name: 'Senior Representative',
    directCommission: 0.325, // 32.50%
    pacCommission: 0.325,
    trailCommission: 0.12 // 12.00%
  },
  DIS: {
    code: '10',
    name: 'District Leader',
    directCommission: 0.35, // 35.00%
    pacCommission: 0.35,
    trailCommission: 0.12 // 12.00%
  },
  DIV: {
    code: '15',
    name: 'Division Leader',
    directCommission: 0.375, // 37.50%
    pacCommission: 0.375,
    trailCommission: 0.18 // 18.00%
  },
  REG: {
    code: '20',
    name: 'Regional Leader',
    directCommission: 0.425, // 42.50%
    pacCommission: 0.425,
    trailCommission: 0.38 // 38.00%
  },
  SRL: {
    code: '23',
    name: 'Senior Regional Leader',
    directCommission: 0.475, // 47.50%
    pacCommission: 0.475,
    trailCommission: 0.44 // 44.00%
  },
  PRL: {
    code: '25',
    name: 'Principal Regional Leader',
    directCommission: 0.50, // 50.00%
    pacCommission: 0.50,
    trailCommission: 0.49 // 49.00%
  },
  RVP: {
    code: '26',
    name: 'Regional Vice President',
    directCommission: 0.62, // 62.00%
    pacCommission: 0.62,
    trailCommission: 0.57 // 57.00%
  }
} as const;

// Generational override rates
export const GENERATIONAL_OVERRIDES = {
  first: {
    name: '1st Generation',
    overrideCommission: 0.061, // 6.10%
    overrideTrail: 0.075 // 7.50%
  },
  second: {
    name: '2nd Generation',
    overrideCommission: 0.026, // 2.60%
    overrideTrail: 0.050 // 5.00%
  },
  third: {
    name: '3rd Generation',
    overrideCommission: 0.017, // 1.70%
    overrideTrail: 0.0225 // 2.25%
  },
  fourth: {
    name: '4th Generation',
    overrideCommission: 0.013, // 1.30%
    overrideTrail: 0.015 // 1.50%
  },
  fifth: {
    name: '5th Generation',
    overrideCommission: 0.008, // 0.80%
    overrideTrail: 0.010 // 1.00%
  },
  sixth: {
    name: '6th Generation',
    overrideCommission: 0.005, // 0.50%
    overrideTrail: 0.0075 // 0.75%
  }
} as const;
