
// Agency settings interface for better type safety
export interface AgencySettings {
  numAgencies: number;
  numRLsPerAgency: number;
}

// AUM breakdown interface
export interface AUMBreakdown {
  personal: number;
  team: number;
  agencies: number;
  total: number;
}

// Production data interface for cards
export interface ProductionData {
  personalAUM?: number;
  teamAUM?: number;
  agencyAUM?: number;
  personalPAC?: number;
  teamPAC?: number;
  agencyPAC?: number;
  numRLs?: number;
  numAgencies?: number;
  numRLsPerAgency?: number;
  projectionYears?: number;
}

// Simulator inputs with proper typing
export interface SimpleSimulationInputs {
  initialInvestment: number;
  numRolloverCases: number;
  pacPerCase: number;
  numPacCases: number;
  projectionYears: number;
  marketGrowth: number;
  numRLs: number;
  // Business Owner agency settings
  businessOwnerAgencies: number;
  businessOwnerRLsPerAgency: number;
  // Branch Office Passive agency settings
  branchOfficeAgencies: number;
  branchOfficeRLsPerAgency: number;
  // AUM data
  aum?: AUMBreakdown;
  // Trail rate
  trailRate?: number;
  // Scenario settings (optional for backwards compatibility)
  scenarioSettings?: import('./scenarioSettings').ScenarioSettings;
}

export interface IncomeBreakdown {
  directEffort: number;
  override?: number;
  recurringPac: number;
  trailIncome: number;
  total: number;
}

export interface AgencyOverride {
  overrideIncome: number;
  recurringPacIncome: number;
  trailIncome: number;
  total: number;
}

export interface SimpleScenarioResults {
  selfEmployed: IncomeBreakdown;
  selfEmployedTeam: IncomeBreakdown & {
    teamOverride: number;
  };
  businessOwner: {
    personalOffice: IncomeBreakdown;
    agencyOverrides: AgencyOverride;
    total: number;
  };
  passiveOwner: {
    recurringPac: number;
    trailIncome: number;
    agencyOverrides: AgencyOverride;
    total: number;
  };
  aum: {
    personal: number;
    team: number;
    agencies: number;
    total: number;
  };
}
