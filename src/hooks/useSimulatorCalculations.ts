import { useMemo, useCallback, useState } from 'react';
import { SimpleSimulationInputs, SimpleScenarioResults } from '@/types/simpleTypes';
import { calculateSimpleScenarios } from '@/utils/simpleCalculator';
import { validateSimulatorInputs, ValidationResult } from '@/utils/validation';

interface CalculationState {
  results: SimpleScenarioResults | null;
  isCalculating: boolean;
  error: string | null;
  validation: ValidationResult;
}

interface UseSimulatorCalculationsReturn {
  results: SimpleScenarioResults | null;
  isCalculating: boolean;
  error: string | null;
  validation: ValidationResult;
  calculate: (inputs: SimpleSimulationInputs) => void;
  clearError: () => void;
}

/**
 * Custom hook for managing simulator calculations with validation and error handling
 */
export const useSimulatorCalculations = (
  initialInputs?: SimpleSimulationInputs
): UseSimulatorCalculationsReturn => {
  const [state, setState] = useState<CalculationState>({
    results: null,
    isCalculating: false,
    error: null,
    validation: { isValid: true, errors: [] }
  });

  // Memoized calculation function
  const performCalculation = useCallback((inputs: SimpleSimulationInputs) => {
    setState(prev => ({ ...prev, isCalculating: true, error: null }));

    try {
      // Validate inputs first
      const validation = validateSimulatorInputs(inputs);
      
      if (!validation.isValid) {
        setState(prev => ({
          ...prev,
          isCalculating: false,
          validation,
          error: `Validation failed: ${validation.errors.map(e => e.message).join(', ')}`
        }));
        return;
      }

      // Perform calculation
      const results = calculateSimpleScenarios(inputs);
      
      setState(prev => ({
        ...prev,
        results,
        isCalculating: false,
        validation,
        error: null
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown calculation error';
      setState(prev => ({
        ...prev,
        isCalculating: false,
        error: errorMessage,
        validation: { isValid: false, errors: [{ field: 'calculation', message: errorMessage }] }
      }));
    }
  }, []);

  // Memoized results calculation for initial inputs
  const initialResults = useMemo(() => {
    if (!initialInputs) return null;
    
    try {
      const validation = validateSimulatorInputs(initialInputs);
      if (validation.isValid) {
        return calculateSimpleScenarios(initialInputs);
      }
    } catch (error) {
      console.warn('Failed to calculate initial results:', error);
    }
    
    return null;
  }, [initialInputs]);

  // Use initial results if no calculation has been performed
  const currentResults = state.results || initialResults;

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    results: currentResults,
    isCalculating: state.isCalculating,
    error: state.error,
    validation: state.validation,
    calculate: performCalculation,
    clearError
  };
};

/**
 * Hook for memoized individual scenario calculations
 */
export const useScenarioCalculation = (
  inputs: SimpleSimulationInputs | null,
  scenarioType: 'selfEmployed' | 'selfEmployedTeam' | 'businessOwner' | 'passiveOwner'
) => {
  return useMemo(() => {
    if (!inputs) return null;
    
    try {
      const validation = validateSimulatorInputs(inputs);
      if (!validation.isValid) return null;
      
      const results = calculateSimpleScenarios(inputs);
      return results[scenarioType];
    } catch (error) {
      console.warn(`Failed to calculate ${scenarioType} scenario:`, error);
      return null;
    }
  }, [inputs, scenarioType]);
};

/**
 * Hook for performance monitoring of calculations
 */
export const useCalculationPerformance = () => {
  const [metrics, setMetrics] = useState<{
    lastCalculationTime: number;
    averageCalculationTime: number;
    calculationCount: number;
  }>({
    lastCalculationTime: 0,
    averageCalculationTime: 0,
    calculationCount: 0
  });

  const measureCalculation = useCallback(<T,>(
    calculationFn: () => T,
    label?: string
  ): T => {
    const startTime = performance.now();
    const result = calculationFn();
    const endTime = performance.now();
    const duration = endTime - startTime;

    setMetrics(prev => {
      const newCount = prev.calculationCount + 1;
      const newAverage = (prev.averageCalculationTime * prev.calculationCount + duration) / newCount;
      
      return {
        lastCalculationTime: duration,
        averageCalculationTime: newAverage,
        calculationCount: newCount
      };
    });

    if (process.env.NODE_ENV === 'development' && label) {
      console.log(`${label} calculation took ${duration.toFixed(2)}ms`);
    }

    return result;
  }, []);

  return { metrics, measureCalculation };
};
