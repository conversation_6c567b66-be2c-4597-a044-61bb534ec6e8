
import { useState, useEffect, useCallback, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useNavigate } from 'react-router-dom';
import CompEdgeHeader from '@/components/CompEdgeHeader';

import ErrorBoundary from '@/components/ErrorBoundary';
import { AlertTriangle } from 'lucide-react';

// Import our new components
import InputPanel from '@/components/simulator/inputs/InputPanel';
import ScenarioCardGrid from '@/components/simulator/simple/ScenarioCardGrid';
import AdvancedSettingsModal from '@/components/simulator/AdvancedSettingsModal';

import { LoadingCard } from '@/components/ui/loading';

// Import utility functions
import { calculateSimpleScenarios } from '@/utils/simpleCalculator';
import { validateSimulatorInputs } from '@/utils/validation';
import { SimpleSimulationInputs } from '@/types/simpleTypes';
import { ScenarioSettings, DEFAULT_SCENARIO_SETTINGS } from '@/types/scenarioSettings';
import { applyScenarioSettingsToInputs } from '@/utils/scenarioSettingsUtils';

const SimpleIncome = () => {
  const navigate = useNavigate();

  // Advanced scenario settings
  const [scenarioSettings, setScenarioSettings] = useState<ScenarioSettings>(DEFAULT_SCENARIO_SETTINGS);

  // Default inputs for simple income simulator
  const [inputs, setInputs] = useState<SimpleSimulationInputs>({
    initialInvestment: 25000,
    numRolloverCases: 5,
    pacPerCase: 250,
    numPacCases: 5,
    projectionYears: scenarioSettings.scenarioDefaults.projectionYears,
    marketGrowth: scenarioSettings.scenarioDefaults.marketGrowthRate,
    numRLs: 3,
    // Business Owner agency settings
    businessOwnerAgencies: scenarioSettings.scenarioDefaults.defaultAgencies,
    businessOwnerRLsPerAgency: scenarioSettings.scenarioDefaults.defaultRLsPerAgency,
    // Branch Office Passive agency settings
    branchOfficeAgencies: scenarioSettings.scenarioDefaults.defaultAgencies,
    branchOfficeRLsPerAgency: scenarioSettings.scenarioDefaults.defaultRLsPerAgency,
    // AUM data will be populated from results
    trailRate: scenarioSettings.investmentVehicle.trailRate
  });

  // Add validation state
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Store calculated results
  const [results, setResults] = useState(null);

  // Debounced calculation to reduce excessive re-renders
  const [debouncedInputs, setDebouncedInputs] = useState(inputs);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedInputs(inputs);
    }, 150); // 150ms debounce

    return () => clearTimeout(timer);
  }, [inputs]);

  // Memoize calculated results with debounced inputs and scenario settings
  const calculatedResults = useMemo(() => {
    try {
      // Apply scenario settings to inputs
      const enhancedInputs = applyScenarioSettingsToInputs(debouncedInputs, scenarioSettings);

      return calculateSimpleScenarios({
        initialInvestment: enhancedInputs.initialInvestment,
        numRolloverCases: enhancedInputs.numRolloverCases,
        pacPerCase: enhancedInputs.pacPerCase,
        numPacCases: enhancedInputs.numPacCases,
        projectionYears: enhancedInputs.projectionYears,
        marketGrowth: enhancedInputs.marketGrowth,
        numRLs: enhancedInputs.numRLs,
        businessOwnerAgencies: enhancedInputs.businessOwnerAgencies,
        businessOwnerRLsPerAgency: enhancedInputs.businessOwnerRLsPerAgency,
        branchOfficeAgencies: enhancedInputs.branchOfficeAgencies,
        branchOfficeRLsPerAgency: enhancedInputs.branchOfficeRLsPerAgency,
        // Pass scenario settings to calculator
        scenarioSettings: scenarioSettings
      });
    } catch (error) {
      console.error('Calculation error:', error);
      return null;
    }
  }, [debouncedInputs, scenarioSettings]);

  // Update results immediately without loading state flashing
  useEffect(() => {
    if (calculatedResults) {
      setResults(calculatedResults);
    }
  }, [calculatedResults]);

  // Manual calculate function for the button (if needed)
  const calculateScenarios = useCallback(() => {
    if (calculatedResults) {
      setResults(calculatedResults);
    }
  }, [calculatedResults]);

  // Handle scenario settings changes
  const handleScenarioSettingsChange = useCallback((newSettings: ScenarioSettings) => {
    setScenarioSettings(newSettings);

    // Update inputs to reflect new defaults if they haven't been manually changed
    setInputs(prev => ({
      ...prev,
      projectionYears: newSettings.scenarioDefaults.projectionYears,
      marketGrowth: newSettings.scenarioDefaults.marketGrowthRate,
      trailRate: newSettings.investmentVehicle.trailRate,
      // Update agency defaults if they match current defaults
      businessOwnerAgencies: prev.businessOwnerAgencies === scenarioSettings.scenarioDefaults.defaultAgencies
        ? newSettings.scenarioDefaults.defaultAgencies
        : prev.businessOwnerAgencies,
      businessOwnerRLsPerAgency: prev.businessOwnerRLsPerAgency === scenarioSettings.scenarioDefaults.defaultRLsPerAgency
        ? newSettings.scenarioDefaults.defaultRLsPerAgency
        : prev.businessOwnerRLsPerAgency,
      branchOfficeAgencies: prev.branchOfficeAgencies === scenarioSettings.scenarioDefaults.defaultAgencies
        ? newSettings.scenarioDefaults.defaultAgencies
        : prev.branchOfficeAgencies,
      branchOfficeRLsPerAgency: prev.branchOfficeRLsPerAgency === scenarioSettings.scenarioDefaults.defaultRLsPerAgency
        ? newSettings.scenarioDefaults.defaultRLsPerAgency
        : prev.branchOfficeRLsPerAgency,
    }));
  }, [scenarioSettings]);

  // Handle input changes - immediate update for better UX
  const handleInputChange = useCallback((name: string, value: number) => {
    // Only apply basic sanitization for extreme values
    let sanitizedValue = value;

    switch (name) {
      case 'initialInvestment':
        if (value < 0) sanitizedValue = 0;
        if (value > 10000000) sanitizedValue = 10000000;
        break;
      case 'numRolloverCases':
      case 'numPacCases':
        if (value < 0) sanitizedValue = 0;
        if (value > 1000) sanitizedValue = 1000;
        break;
      case 'pacPerCase':
        if (value < 0) sanitizedValue = 0;
        if (value > 100000) sanitizedValue = 100000;
        break;
      case 'projectionYears':
        if (value < 1) sanitizedValue = 1;
        if (value > 100) sanitizedValue = 100;
        break;
      case 'marketGrowth':
        if (value < -1) sanitizedValue = -1;
        if (value > 5) sanitizedValue = 5;
        break;
      case 'numRLs':
      case 'businessOwnerAgencies':
      case 'businessOwnerRLsPerAgency':
      case 'branchOfficeAgencies':
      case 'branchOfficeRLsPerAgency':
        if (value < 0) sanitizedValue = 0;
        if (value > 100) sanitizedValue = 100;
        break;
    }

    const newInputs = { ...inputs, [name]: sanitizedValue };

    // Validate inputs
    const validation = validateSimulatorInputs(newInputs);
    const criticalErrors = validation.errors.filter(error =>
      error.message.includes('cannot be negative') ||
      error.message.includes('must be at least')
    );
    setValidationErrors(criticalErrors.map(e => e.message));

    setInputs(newInputs);
  }, [inputs]);

  // No need for additional useEffect since results update automatically

  return (
    <ErrorBoundary>
      <div className="h-screen bg-gray-50 flex flex-col">
        <CompEdgeHeader />

        <ScrollArea className="flex-1">
          <main className="container py-8">
            <Card className="bg-white">
              <div className="p-6">
                <div className="flex justify-between items-center mb-8">
                  <Tabs defaultValue="simple" onValueChange={(value) => {
                    if (value === "advanced") navigate("/advanced");
                  }} className="flex-1">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="simple">Simple Income Explainer</TabsTrigger>
                      <TabsTrigger value="advanced">Advanced Calculator</TabsTrigger>
                    </TabsList>
                  </Tabs>

                  {/* Advanced Settings Modal */}
                  <div className="ml-4">
                    <AdvancedSettingsModal
                      settings={scenarioSettings}
                      onSettingsChange={handleScenarioSettingsChange}
                    />
                  </div>
                </div>

                <Tabs defaultValue="simple" onValueChange={(value) => {
                  if (value === "advanced") navigate("/advanced");
                }}>

                  <TabsContent value="simple" className="space-y-8">
                    {validationErrors.length > 0 && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2 text-red-700">
                          <AlertTriangle className="h-4 w-4" />
                          <span className="font-medium">Input Validation Errors:</span>
                        </div>
                        <ul className="mt-2 text-sm text-red-600 list-disc list-inside">
                          {validationErrors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <InputPanel
                      inputs={inputs}
                      onChange={handleInputChange}
                      onCalculate={calculateScenarios}
                    />

                    {results ? (
                      <>
                        <ScenarioCardGrid
                          results={results}
                          inputs={inputs}
                          scenarioSettings={scenarioSettings}
                          onInputChange={handleInputChange}
                        />

                      </>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {Array.from({ length: 4 }).map((_, i) => (
                          <LoadingCard key={i} title="Loading..." />
                        ))}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            </Card>
          </main>
        </ScrollArea>
      </div>
    </ErrorBoundary>
  );
};

export default SimpleIncome;
