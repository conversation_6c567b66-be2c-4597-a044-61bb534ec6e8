import React from 'react';
import { formatCurrency } from '@/utils/formatting';
import { cn } from '@/lib/utils';

interface CalculationItem {
  label: string;
  value: string | number;
  isResult?: boolean;
}

interface CalculationDetailsProps {
  title: string;
  amount: number;
  calculations: CalculationItem[];
  className?: string;
}

const CalculationDetails: React.FC<CalculationDetailsProps> = ({
  title,
  amount,
  calculations,
  className
}) => {
  return (
    <div className={cn("p-4 flex flex-col h-full", className)}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">{title}</h3>
        <span className="text-lg font-bold">{formatCurrency(amount)}</span>
      </div>

      <div className="space-y-2 flex-grow">
        {calculations.map((item, index) => (
          <div
            key={index}
            className={cn(
              "flex justify-between py-1",
              item.isResult ? "border-t pt-2 mt-2 font-semibold" : "",
              index === calculations.length - 1 ? "pb-0" : ""
            )}
          >
            <span>{item.label}:</span>
            <span>
              {typeof item.value === 'number'
                ? formatCurrency(item.value)
                : item.value}
            </span>
          </div>
        ))}
      </div>

      <div className="mt-4 text-center text-sm text-muted-foreground">
        Click to return
      </div>
    </div>
  );
};

export default CalculationDetails;
