
import React, { ReactNode } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface SummaryCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  className?: string;
  valueClassName?: string;
  children?: ReactNode;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ 
  title, 
  value, 
  subtitle, 
  className,
  valueClassName,
  children 
}) => {
  return (
    <Card className={cn("bg-white shadow-lg border border-gray-200 hover:shadow-xl transition-shadow duration-200", className)}>
      <CardContent className="p-6">
        <div className="text-sm font-medium text-gray-600 uppercase tracking-wide">{title}</div>
        <div className={cn("text-3xl font-bold mt-2 mb-1", valueClassName || "text-gray-900")}>{value}</div>
        {subtitle && (
          <div className="text-sm text-gray-500 leading-relaxed">{subtitle}</div>
        )}
        {children}
      </CardContent>
    </Card>
  );
};

export default SummaryCard;
