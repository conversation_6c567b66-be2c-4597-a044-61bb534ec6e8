
import React from 'react';
import { formatCurrency } from '@/utils/formatting';
import TimelineItem from '../common/TimelineItem';
import { useColorTheme } from '@/contexts/ThemeContext';

interface TimelineContentProps {
  results: any;
  viewMode: 'agent' | 'broker';
}

const TimelineContent: React.FC<TimelineContentProps> = ({ results, viewMode }) => {
  const { colorTheme } = useColorTheme();

  const accentColor = colorTheme === 'wealthtech'
    ? 'bg-emerald-100 border-emerald-300 text-emerald-800'
    : 'bg-red-100 border-red-300 text-red-800';

  return (
    <div className="relative border-l-2 border-gray-200 pl-10 space-y-12 pb-6">
      {/* Month 1 */}
      <TimelineItem
        label="Month 1"
        marker="1"
        title="Month 1: Initial Rollover"
        className={accentColor}
      >
        <div className="font-semibold">
          Direct Commission: {formatCurrency(results.personalCommission)}
        </div>
        <div className="text-sm mt-1">
          Assets Under Management: {formatCurrency(results.totalInitialInvestment)}
        </div>
      </TimelineItem>

      {/* Month 6 */}
      <TimelineItem
        label="Month 6"
        marker="6"
        title="Month 6: PAC Stacking"
        className={accentColor}
      >
        <div className="font-semibold">
          Monthly PAC Income: {formatCurrency(results.rolling12MonthAvg / 2)}
        </div>
        <div className="text-sm mt-1">
          Assets Under Management: {formatCurrency(results.totalInitialInvestment + results.totalContributions / 2)}
        </div>
      </TimelineItem>

      {/* Month 12 */}
      <TimelineItem
        label="Month 12"
        marker="12"
        title="Month 12: Full PAC + Trail Begins"
        className={accentColor}
      >
        <div className="font-semibold">
          Total Direct Income: {formatCurrency(results.totalCommission)}
        </div>
        <div className="text-sm mt-1">
          Assets Under Management: {formatCurrency(results.totalInvested)}
        </div>
        {results.trailProjections && results.trailProjections.length > 0 && (
          <div className="text-sm mt-1">
            Trail Income Beginning: {formatCurrency(results.trailProjections[0]?.totalTrail || 0)}
          </div>
        )}
      </TimelineItem>

      {/* Year 5 */}
      <TimelineItem
        label="Year 5"
        marker="5Y"
        title="Year 5: Long-term Projection"
        className={accentColor}
      >
        {results.trailProjections && results.trailProjections.length > 0 && (
          <>
            <div className="font-semibold">
              Annual Trail Income: {formatCurrency(results.trailProjections.find((p: any) => p.year === 5)?.totalTrail || 0)}
            </div>
            <div className="text-sm mt-1">
              Projected AUM: {formatCurrency(results.trailProjections.find((p: any) => p.year === 5)?.aum || 0)}
            </div>
            <div className="text-sm mt-1">
              Monthly Trail Income: {formatCurrency((results.trailProjections.find((p: any) => p.year === 5)?.totalTrail || 0) / 12)}
            </div>
          </>
        )}
      </TimelineItem>
    </div>
  );
};

export default TimelineContent;
