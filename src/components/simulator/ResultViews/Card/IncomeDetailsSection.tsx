
import React from 'react';
import { formatCurrency } from '@/utils/formatting';
import SummaryCard from '../common/SummaryCard';

interface IncomeDetailsSectionProps {
  personalCommission: number;
  totalCommission: number;
  rolling12MonthAvg: number;
}

const IncomeDetailsSection: React.FC<IncomeDetailsSectionProps> = ({
  personalCommission,
  totalCommission,
  rolling12MonthAvg
}) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Income Breakdown</h3>
        <span className="text-sm text-gray-500">First year projections</span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <SummaryCard
          title="Initial Commission"
          value={formatCurrency(personalCommission)}
          subtitle="Month 1 income"
          className="border-l-4 border-l-blue-500"
        />
        <SummaryCard
          title="Total First Year"
          value={formatCurrency(totalCommission)}
          subtitle="All commissions"
          valueClassName="text-green-600"
          className="border-l-4 border-l-green-500"
        />
        <SummaryCard
          title="Monthly Average"
          value={formatCurrency(rolling12MonthAvg / 12)}
          subtitle={`Annual: ${formatCurrency(rolling12MonthAvg)}`}
          valueClassName="text-purple-600"
          className="border-l-4 border-l-purple-500"
        />
      </div>
    </div>
  );
};

export default IncomeDetailsSection;
