
import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from '@/utils/formatting';

interface InitialRolloverCardProps {
  personalCommission: number;
  totalInitialInvestment: number;
}

const InitialRolloverCard: React.FC<InitialRolloverCardProps> = ({
  personalCommission,
  totalInitialInvestment
}) => {
  return (
    <Card className="bg-white shadow-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Initial Rollover</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div>
            <p className="text-sm text-gray-500">Commission</p>
            <p className="text-xl font-semibold">{formatCurrency(personalCommission)}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Investment Amount</p>
            <p className="text-xl font-semibold">{formatCurrency(totalInitialInvestment)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default InitialRolloverCard;
