
import React from 'react';
import { formatCurrency } from '@/utils/formatting';
import SummaryCard from '../common/SummaryCard';
import { Card } from "@/components/ui/card";

interface InvestmentSummarySectionProps {
  totalInitialInvestment: number;
  totalContributions: number;
  totalInvested: number;
}

const InvestmentSummarySection: React.FC<InvestmentSummarySectionProps> = ({
  totalInitialInvestment,
  totalContributions,
  totalInvested
}) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Investment Summary</h3>
        <span className="text-sm text-gray-500">Client investment totals</span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <SummaryCard
          title="Initial Investment"
          value={formatCurrency(totalInitialInvestment)}
          subtitle="One-time rollover"
          className="border-l-4 border-l-orange-500"
        />
        <SummaryCard
          title="PAC Contributions"
          value={formatCurrency(totalContributions)}
          subtitle="Monthly contributions"
          className="border-l-4 border-l-blue-500"
        />
        <SummaryCard
          title="Total Invested"
          value={formatCurrency(totalInvested)}
          subtitle="Principal amount"
          valueClassName="text-green-600"
          className="border-l-4 border-l-green-500"
        />
      </div>
    </div>
  );
};

export default InvestmentSummarySection;
