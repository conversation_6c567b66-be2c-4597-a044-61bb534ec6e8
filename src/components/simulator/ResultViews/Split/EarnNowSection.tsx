
import React from 'react';
import { formatCurrency } from '@/utils/formatting';
import SplitItem from '../common/SplitItem';
import SplitColumn from '../common/SplitColumn';

interface EarnNowSectionProps {
  personalCommission: number;
  rolling12MonthAvg: number;
  totalCommission: number;
}

const EarnNowSection: React.FC<EarnNowSectionProps> = ({
  personalCommission,
  rolling12MonthAvg,
  totalCommission
}) => {
  return (
    <SplitColumn title="Earn Now">
      <SplitItem
        label="Month 1"
        value={formatCurrency(personalCommission)}
        subtitle="Initial rollover commission"
      />

      <SplitItem
        label="Month 6"
        value={formatCurrency(rolling12MonthAvg / 2)}
        subtitle="Mid-year PAC commission"
      />

      <SplitItem
        label="Month 12"
        value={formatCurrency(totalCommission)}
        subtitle="Full-year direct income"
      />

      <div className="pt-4 border-t">
        <div className="text-lg font-semibold">
          First-Year Total: {formatCurrency(totalCommission)}
        </div>
      </div>
    </SplitColumn>
  );
};

export default EarnNowSection;
