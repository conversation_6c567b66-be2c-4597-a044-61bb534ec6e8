
import React from 'react';
import { formatCurrency } from '@/utils/formatting';
import SplitItem from '../common/SplitItem';
import SplitColumn from '../common/SplitColumn';
import { useColorTheme } from '@/contexts/ThemeContext';

interface EarnLaterSectionProps {
  trailProjections: any[];
}

const EarnLaterSection: React.FC<EarnLaterSectionProps> = ({
  trailProjections
}) => {
  const { colorTheme } = useColorTheme();

  if (!trailProjections || trailProjections.length === 0) {
    return (
      <SplitColumn title="Earn Later">
        <div className="text-muted-foreground">No trail projections available</div>
      </SplitColumn>
    );
  }

  const year1Trail = trailProjections.find((p) => p.year === 1);
  const year5Trail = trailProjections.find((p) => p.year === 5);
  const year10Trail = trailProjections.find((p) => p.year === 10);

  const accentColor = colorTheme === 'wealthtech' ? '#10b981' : '#ef4444';

  return (
    <SplitColumn title="Earn Later">
      <SplitItem
        label="Year 1"
        value={formatCurrency(year1Trail?.totalTrail || 0)}
        subtitle="First trail income"
        valueClassName={`text-[${accentColor}]`}
      />

      <SplitItem
        label="Year 5"
        value={formatCurrency(year5Trail?.totalTrail || 0)}
        subtitle={`Mid-term trail (monthly: ${formatCurrency((year5Trail?.totalTrail || 0) / 12)})`}
        valueClassName={`text-[${accentColor}]`}
      />

      <SplitItem
        label="Year 10"
        value={formatCurrency(year10Trail?.totalTrail || 0)}
        subtitle="Long-term trail income"
        valueClassName={`text-[${accentColor}]`}
      />

      <div className="pt-4 border-t">
        <div className="text-lg font-semibold">
          10-Year Total Trails: {formatCurrency(
            trailProjections
              .filter((p) => p.year <= 10)
              .reduce((sum, p) => sum + p.totalTrail, 0)
          )}
        </div>
      </div>
    </SplitColumn>
  );
};

export default EarnLaterSection;
