
import React from 'react';
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useColorTheme } from '@/contexts/ThemeContext';
import { DollarSign, TrendingUp, LineChart } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { formatCurrency } from '@/utils/formatting';

interface SimulatorSettingsProps {
  avgInitialInvestment: number;
  setAvgInitialInvestment: (value: number) => void;
  avgMonthlyContribution: number;
  setAvgMonthlyContribution: (value: number) => void;
  fundType: 'equity' | 'income' | 'other';
  setFundType: (value: 'equity' | 'income' | 'other') => void;
  marketGrowth: number;
  setMarketGrowth: (value: number) => void;
}

const SimulatorSettings: React.FC<SimulatorSettingsProps> = ({
  avgInitialInvestment,
  setAvgInitialInvestment,
  avgMonthlyContribution,
  setAvgMonthlyContribution,
  fundType,
  setFundType,
  marketGrowth,
  setMarketGrowth
}) => {
  const { colorTheme, debugMode } = useColorTheme();

  const MotionDiv = motion.div;

  return (
    <MotionDiv
      className={`border rounded-lg p-6 backdrop-blur-sm space-y-6 ${colorTheme === 'wealthtech' ? 'bg-wealthtech-tile-bg border-wealthtech-tile-border shadow-wealthtech-tile-shadow' : 'bg-primerica-tile-bg border-primerica-tile-border shadow-primerica-tile-shadow'}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      <div className="flex items-center gap-2">
        <LineChart className={colorTheme === 'wealthtech' ? 'text-emerald-500' : 'text-red-500'} size={18} />
        <h3 className="font-display font-medium">
          Advanced Settings
        </h3>
      </div>

      {/* Average Initial Investment */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <DollarSign size={16} className="text-gray-500" />
          <label htmlFor="avgInitial" className="text-sm font-medium text-gray-900">
            Average Initial Investment
          </label>
        </div>
        <div className="flex items-center">
          <span className="mr-2 text-gray-500">$</span>
          <Input
            id="avgInitial"
            type="number"
            value={avgInitialInvestment}
            onChange={(e) => setAvgInitialInvestment(Number(e.target.value))}
            min={1000}
            max={1000000}
            step={1000}
            className={cn(
              colorTheme === 'primerica'
                ? 'border-primerica-tile-border focus:border-primerica-text-accent'
                : 'border-wealthtech-tile-border focus:border-wealthtech-text-accent'
            )}
          />
        </div>
      </div>

      {/* Average Monthly Contribution */}
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <TrendingUp size={16} className="text-gray-500" />
            <label className="text-sm font-medium text-gray-900">
              Average Monthly Contribution
            </label>
          </div>
          <span
            className={`text-sm font-medium font-display ${colorTheme === 'wealthtech' ? 'text-emerald-500' : 'text-red-500'}`}
          >
            ${avgMonthlyContribution}
          </span>
        </div>
        <Slider
          value={[avgMonthlyContribution]}
          min={50}
          max={650}
          step={25}
          onValueChange={(value) => setAvgMonthlyContribution(value[0])}
          className="py-4"
          showTooltip
          formatTooltip={formatCurrency}
        />
        <div className="text-xs text-gray-500">
          Range: $50 - $650 (2025 IRA limits)
        </div>
      </div>

      {/* Fund Type Selector */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-900">
          Fund Type
        </label>
        <ToggleGroup
          type="single"
          value={fundType}
          onValueChange={(value) => value && setFundType(value as 'equity' | 'income' | 'other')}
          className="justify-start"
        >
          <ToggleGroupItem
            value="equity"
            className={colorTheme === 'primerica'
              ? 'data-[state=on]:bg-red-500 data-[state=on]:text-white'
              : 'data-[state=on]:bg-emerald-500 data-[state=on]:text-white'
            }
          >
            Equity
          </ToggleGroupItem>
          <ToggleGroupItem
            value="income"
            className={colorTheme === 'primerica'
              ? 'data-[state=on]:bg-red-500 data-[state=on]:text-white'
              : 'data-[state=on]:bg-emerald-500 data-[state=on]:text-white'
            }
          >
            Income
          </ToggleGroupItem>
          <ToggleGroupItem
            value="other"
            className={colorTheme === 'primerica'
              ? 'data-[state=on]:bg-red-500 data-[state=on]:text-white'
              : 'data-[state=on]:bg-emerald-500 data-[state=on]:text-white'
            }
          >
            Other
          </ToggleGroupItem>
        </ToggleGroup>
        <div className="text-xs text-gray-500">
          {fundType === 'equity' && (
            <div className="flex items-center gap-1">
              <span>Higher growth potential, CDR cap: 5.75%, Trail: 0.25%</span>
              {debugMode && (
                <Tooltip>
                  <TooltipTrigger>
                    <DollarSign size={12} />
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-xs">
                      <p>CDR Cap: 5.75%</p>
                      <p>Trail Rate: 0.25%</p>
                      <p>Typical Use: Stock funds, balanced funds</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          )}
          {fundType === 'income' && (
            <div className="flex items-center gap-1">
              <span>Moderate growth, CDR cap: 4.75%, Trail: 0.15%</span>
              {debugMode && (
                <Tooltip>
                  <TooltipTrigger>
                    <DollarSign size={12} />
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-xs">
                      <p>CDR Cap: 4.75%</p>
                      <p>Trail Rate: 0.15%</p>
                      <p>Typical Use: Bond funds, income-oriented funds</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          )}
          {fundType === 'other' && (
            <div className="flex items-center gap-1">
              <span>Conservative, CDR cap: 4.00%, Trail: 0.10%</span>
              {debugMode && (
                <Tooltip>
                  <TooltipTrigger>
                    <DollarSign size={12} />
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-xs">
                      <p>CDR Cap: 4.00%</p>
                      <p>Trail Rate: 0.10%</p>
                      <p>Typical Use: Money market, ultra-conservative</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Market Growth % */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-900">
          Annual Market Growth
        </label>
        <ToggleGroup
          type="single"
          value={marketGrowth.toString()}
          onValueChange={(value) => value && setMarketGrowth(parseFloat(value))}
          className="justify-center w-full"
        >
          <ToggleGroupItem
            value="0.08"
            className={`flex-1 ${colorTheme === 'primerica'
              ? 'data-[state=on]:bg-red-500 data-[state=on]:text-white'
              : 'data-[state=on]:bg-emerald-500 data-[state=on]:text-white'
            }`}
          >
            8%
          </ToggleGroupItem>
          <ToggleGroupItem
            value="0.09"
            className={`flex-1 ${colorTheme === 'primerica'
              ? 'data-[state=on]:bg-red-500 data-[state=on]:text-white'
              : 'data-[state=on]:bg-emerald-500 data-[state=on]:text-white'
            }`}
          >
            9%
          </ToggleGroupItem>
          <ToggleGroupItem
            value="0.10"
            className={`flex-1 ${colorTheme === 'primerica'
              ? 'data-[state=on]:bg-red-500 data-[state=on]:text-white'
              : 'data-[state=on]:bg-emerald-500 data-[state=on]:text-white'
            }`}
          >
            10%
          </ToggleGroupItem>
          <ToggleGroupItem
            value="0.11"
            className={`flex-1 ${colorTheme === 'primerica'
              ? 'data-[state=on]:bg-red-500 data-[state=on]:text-white'
              : 'data-[state=on]:bg-emerald-500 data-[state=on]:text-white'
            }`}
          >
            11%
          </ToggleGroupItem>
        </ToggleGroup>
        {debugMode && (
          <div className="text-xs mt-1 text-gray-500">
            This rate affects AUM growth in trail calculations and future value projections.
          </div>
        )}
      </div>
    </MotionDiv>
  );
};

export default SimulatorSettings;
