import React, { useState, useEffect, memo } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { formatNumberWithCommas, parseFormattedNumber } from '@/utils/formatting';

interface MoneyInputProps {
  id: string;
  name: string;
  value: number;
  label: string;
  step?: number;
  onChange: (name: string, value: number) => void;
}

const MoneyInput = memo(({
  id,
  name,
  value,
  label,
  step = 1,
  onChange
}: MoneyInputProps) => {
  const [localValue, setLocalValue] = useState(formatNumberWithCommas(value));
  const [isFocused, setIsFocused] = useState(false);

  // Only update local value when prop changes AND field is not focused
  useEffect(() => {
    if (!isFocused) {
      setLocalValue(formatNumberWithCommas(value));
    }
  }, [value, isFocused]);

  const handleLocalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Allow only numbers and commas
    const cleanValue = newValue.replace(/[^0-9,]/g, '');

    // Parse the number to validate it
    const numValue = cleanValue === '' ? 0 : parseFormattedNumber(cleanValue);

    if (!isNaN(numValue)) {
      // Format with commas for display
      const formattedValue = cleanValue === '' ? '' : formatNumberWithCommas(numValue);
      setLocalValue(formattedValue);
      onChange(name, numValue);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    // Remove commas when focused for easier editing
    const numValue = parseFormattedNumber(localValue);
    if (!isNaN(numValue) && numValue !== 0) {
      setLocalValue(numValue.toString());
    } else if (numValue === 0) {
      setLocalValue('');
    }
  };

  const handleLocalBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    const numValue = parseFormattedNumber(localValue);
    if (isNaN(numValue)) {
      setLocalValue('0');
      onChange(name, 0);
    } else {
      // Format with commas when not focused
      setLocalValue(formatNumberWithCommas(numValue));
      onChange(name, numValue);
    }
  };

  return (
    <div>
      <Label htmlFor={id} className="text-sm font-medium block mb-2">{label}</Label>
      <div className="relative">
        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
        <Input
          id={id}
          name={name}
          type="text"
          value={localValue}
          onChange={handleLocalChange}
          onFocus={handleFocus}
          onBlur={handleLocalBlur}
          className="pl-7 h-10 w-full"
          placeholder="0"
        />
      </div>
    </div>
  );
});

MoneyInput.displayName = 'MoneyInput';

export default MoneyInput;
