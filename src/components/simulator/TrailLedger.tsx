
import React, { useState } from 'react';
import { formatCurrency } from '@/utils/formatting';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp, Calendar } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface TrailLedgerProps {
  trailProjections: {
    year: number;
    aum: number;
    personalTrail: number;
    overrideTrail: number;
    totalTrail: number;
    monthlyTrail?: number;
  }[];
  selectedYear: 5 | 10 | 15 | 20;
  onYearChange: (year: 5 | 10 | 15 | 20) => void;
}

const TrailLedger: React.FC<TrailLedgerProps> = ({
  trailProjections,
  selectedYear,
  onYearChange
}) => {
  const [expanded, setExpanded] = useState(false);

  // Filter and organize projections for display
  const displayYears = [5, 10, 15, 20];
  const keyYearProjections = displayYears.map(year => {
    return trailProjections.find(p => p.year === year) || null;
  }).filter(Boolean);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-display font-medium flex items-center gap-2">
          <Calendar className="h-5 w-5 text-financial-green" />
          Trail Income Ledger
        </h3>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-1"
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? (
            <>
              <span className="text-sm">Collapse</span>
              <ChevronUp className="h-4 w-4" />
            </>
          ) : (
            <>
              <span className="text-sm">Expand All Years</span>
              <ChevronDown className="h-4 w-4" />
            </>
          )}
        </Button>
      </div>

      {/* Year toggle buttons */}
      <div className="flex gap-2 mb-4">
        {displayYears.map(year => (
          <Button
            key={year}
            variant={selectedYear === year ? "default" : "outline"}
            size="sm"
            onClick={() => onYearChange(year as 5 | 10 | 15 | 20)}
            className={`${
              selectedYear === year ? 'bg-financial-green text-white' : ''
            } rounded-full`}
          >
            {year}y
          </Button>
        ))}
      </div>

      {/* Selected year highlight */}
      {trailProjections.find(p => p.year === selectedYear) && (
        <motion.div
          className="p-5 glass rounded-2xl border border-financial-green/30 mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-display font-medium text-financial-green">Year {selectedYear} Projection</h4>
            <span className="px-3 py-1 text-xs font-medium rounded-full bg-financial-green/20 text-financial-green">Selected</span>
          </div>

          <div className="grid grid-cols-3 gap-4">
            {trailProjections.find(p => p.year === selectedYear) && (
              <>
                <motion.div
                  className="space-y-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.1 }}
                >
                  <p className="text-sm text-muted-foreground">Assets Under Management</p>
                  <p className="text-xl font-display font-semibold">
                    {formatCurrency(trailProjections.find(p => p.year === selectedYear)!.aum)}
                  </p>
                </motion.div>

                <motion.div
                  className="space-y-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <p className="text-sm text-muted-foreground">Annual Trail Income</p>
                  <p className="text-xl font-display font-semibold text-financial-green">
                    {formatCurrency(trailProjections.find(p => p.year === selectedYear)!.totalTrail)}
                  </p>
                </motion.div>

                <motion.div
                  className="space-y-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  <p className="text-sm text-muted-foreground">Monthly Trail Income</p>
                  <p className="text-xl font-display font-semibold text-financial-green">
                    {formatCurrency(trailProjections.find(p => p.year === selectedYear)!.totalTrail / 12)}
                  </p>
                </motion.div>
              </>
            )}
          </div>
        </motion.div>
      )}

      {/* Year-by-year grid or expanded view */}
      <motion.div
        variants={container}
        initial="hidden"
        animate="show"
        className={`grid gap-4 ${expanded ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'}`}
      >
        {(expanded ? trailProjections : keyYearProjections).map((projection) => (
          <motion.div
            key={projection.year}
            variants={item}
            className={`p-4 glass rounded-2xl ${projection.year === selectedYear ? 'border-2 border-financial-green' : 'border border-white/20'}`}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center justify-between mb-2">
              <h5 className="font-display font-semibold">Year {projection.year}</h5>
              {[1, 5, 10, 15, 20].includes(projection.year) && (
                <span className="text-xs px-2 py-0.5 rounded-full bg-financial-blue/10 text-financial-blue dark:bg-white/10 dark:text-white">
                  {projection.year === 1 ? 'First' : `${projection.year}Y`}
                </span>
              )}
            </div>

            <div className="space-y-3 mt-3">
              <div>
                <p className="text-xs text-muted-foreground">AUM</p>
                <p className="text-base font-display font-medium">{formatCurrency(projection.aum)}</p>
              </div>

              <div>
                <p className="text-xs text-muted-foreground">Annual Trail</p>
                <p className="text-base font-display font-medium text-financial-green">
                  {formatCurrency(projection.totalTrail)}
                </p>
              </div>

              <div>
                <p className="text-xs text-muted-foreground">Monthly Trail</p>
                <p className="text-base font-display font-medium text-financial-green">
                  {formatCurrency(projection.monthlyTrail || projection.totalTrail / 12)}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {!expanded && trailProjections.length > 4 && (
        <div className="flex justify-center mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setExpanded(true)}
            className="group flex items-center gap-2"
          >
            <span>View All {trailProjections.length} Years</span>
            <ChevronDown className="h-4 w-4 group-hover:animate-bounce" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default TrailLedger;
