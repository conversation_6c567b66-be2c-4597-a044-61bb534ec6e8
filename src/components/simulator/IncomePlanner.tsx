
import React from 'react';
import { Brain, TrendingUp, Users, DollarSign, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { formatCurrency } from '@/utils/formatting';

interface IncomePlannerProps {
  durationYears: number;
  trailProjections?: {
    year: number;
    aum: number;
    totalTrail: number;
  }[];
  isOpen: boolean;
  onClose: () => void;
}

const IncomePlanner: React.FC<IncomePlannerProps> = ({
  durationYears,
  trailProjections,
  isOpen,
  onClose
}) => {
  if (!isOpen || !trailProjections || trailProjections.length === 0) return null;

  // Get relevant projections for calculations
  const lastProjection = trailProjections[trailProjections.length - 1];
  const year5Projection = trailProjections.find(p => p.year === 5) || trailProjections[0];
  const year10Projection = trailProjections.find(p => p.year === 10) || lastProjection;

  // Calculate cumulative income over time
  const calculatedIncomeTotal = trailProjections.reduce((sum, projection) => {
    return sum + projection.totalTrail;
  }, 0);

  // Simple tips based on projections
  const tips = [
    {
      icon: <Users size={18} />,
      tip: `Add 2 agents at 50% productivity → +$${Math.round(year5Projection.totalTrail * 0.15 / 12).toLocaleString()}/month override`
    },
    {
      icon: <TrendingUp size={18} />,
      tip: `Increasing PACs from $300 to $400 adds ~$${Math.round(year5Projection.totalTrail * 0.33).toLocaleString()}/year in trails by Year 5`
    },
    {
      icon: <DollarSign size={18} />,
      tip: `At year ${durationYears}, your AUM would be ${formatCurrency(lastProjection.aum)} generating ${formatCurrency(lastProjection.totalTrail)}/year`
    }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed right-0 top-20 h-auto max-h-[80vh] w-80 bg-white/10 dark:bg-financial-blue/30 backdrop-blur-xl border-l border-t border-b border-white/20 dark:border-white/10 rounded-l-3xl shadow-2xl overflow-hidden z-40"
          initial={{ x: '100%' }}
          animate={{ x: 0 }}
          exit={{ x: '100%' }}
          transition={{ type: 'spring', damping: 25, stiffness: 200 }}
        >
          <div className="flex items-center justify-between p-4 border-b border-white/10">
            <div className="flex items-center gap-2">
              <Brain size={22} className="text-financial-green" />
              <h3 className="font-display font-bold text-xl">Income Planner</h3>
            </div>
            <motion.button
              className="p-1 rounded-full hover:bg-white/10"
              onClick={onClose}
              whileHover={{ rotate: 90 }}
              transition={{ duration: 0.2 }}
            >
              <X size={18} />
            </motion.button>
          </div>

          <div className="p-5 overflow-y-auto">
            <div className="mb-6">
              <p className="text-sm text-muted-foreground mb-2">If you sustain this production level for {durationYears} years...</p>

              <motion.div
                className="glass p-4 rounded-xl mb-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <p className="text-sm text-muted-foreground">Cumulative Total Income</p>
                <p className="text-2xl font-display font-bold text-financial-green">
                  {formatCurrency(calculatedIncomeTotal)}
                </p>
              </motion.div>

              <div className="grid grid-cols-2 gap-3">
                <motion.div
                  className="glass p-3 rounded-xl"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <p className="text-xs text-muted-foreground">Year 5 Trail</p>
                  <p className="text-lg font-display font-semibold">
                    {formatCurrency(year5Projection.totalTrail)}
                  </p>
                </motion.div>

                <motion.div
                  className="glass p-3 rounded-xl"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <p className="text-xs text-muted-foreground">Year 10 Trail</p>
                  <p className="text-lg font-display font-semibold">
                    {formatCurrency(year10Projection.totalTrail)}
                  </p>
                </motion.div>
              </div>
            </div>

            <div className="space-y-3">
              <p className="text-sm font-medium">Quick Tips</p>

              {tips.map((tip, index) => (
                <motion.div
                  key={index}
                  className="flex items-start gap-3 p-3 glass rounded-xl"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 + (index * 0.1) }}
                >
                  <div className="rounded-full p-2 bg-financial-green/20 text-financial-green">
                    {tip.icon}
                  </div>
                  <p className="text-sm">{tip.tip}</p>
                </motion.div>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t border-white/10">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">AUM Growth Projection</p>
              </div>
              <div className="h-40 mt-2 rounded-xl glass overflow-hidden">
                {/* Simplified AUM chart - we'd implement a proper chart here */}
                <div className="h-full w-full relative p-2">
                  <div className="absolute bottom-0 left-0 w-full h-1/5 bg-financial-green/20 rounded-t-xl"></div>
                  <div className="absolute bottom-0 left-0 w-3/4 h-2/5 bg-financial-green/30 rounded-t-xl"></div>
                  <div className="absolute bottom-0 left-0 w-1/2 h-3/5 bg-financial-green/40 rounded-t-xl"></div>
                  <div className="absolute bottom-0 left-0 w-1/4 h-4/5 bg-financial-green/50 rounded-t-xl"></div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default IncomePlanner;
