import React, { useState, useEffect } from 'react';
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Settings, Users } from "lucide-react";
import { Input } from "@/components/ui/input";
import { calculateCommissions } from '@/utils/commissionCalculator';
import { formatCurrency } from '@/utils/formatting';
import { fundTypes } from '@/simConfig/fundTypes';
import { repInfo } from '@/simConfig/repInfo';
import SimulatorResults from './SimulatorResults';
import SimulatorSettings from './SimulatorSettings';
import MultiGenSettings from './MultiGenSettings';
const SimulatorForm = () => {
  // Role toggle state (Agent vs Broker)
  const [viewMode, setViewMode] = useState<'agent' | 'broker'>('agent');

  // Settings panel state
  const [showSettings, setShowSettings] = useState(false);
  const [avgInitialInvestment, setAvgInitialInvestment] = useState(50000);
  const [avgMonthlyContribution, setAvgMonthlyContribution] = useState(300);
  const [fundType, setFundType] = useState<'equity' | 'income' | 'other'>('equity');
  const [marketGrowth, setMarketGrowth] = useState(0.08);

  // Form state for user inputs
  const [rolloverCases, setRolloverCases] = useState(5);
  const [monthlyCases, setMonthlyCases] = useState(5);
  const [durationYears, setDurationYears] = useState(10);

  // Trail projection year toggle
  const [selectedTrailYear, setSelectedTrailYear] = useState<5 | 10 | 20>(5);

  // Get the correct BP percentages for display from repInfo
  const agentBP = repInfo.agent.bp * 100;
  const brokerBP = repInfo.broker.bp * 100;

  // Broker view specific inputs
  const [numAgents, setNumAgents] = useState(5);
  const [avgAgentRollover, setAvgAgentRollover] = useState(3);
  const [avgAgentMonthly, setAvgAgentMonthly] = useState(3);

  // Broker view specific settings
  const [showAgentSettings, setShowAgentSettings] = useState(false);
  const [agentInitialInvestment, setAgentInitialInvestment] = useState(30000);
  const [agentMonthlyContribution, setAgentMonthlyContribution] = useState(200);

  // Multi-generation override settings
  const [enableMultiGen, setEnableMultiGen] = useState(false);
  const [genOneAgents, setGenOneAgents] = useState(5);
  const [genTwoAgents, setGenTwoAgents] = useState(10);
  const [genThreeAgents, setGenThreeAgents] = useState(15);
  const [genOneRolloverCases, setGenOneRolloverCases] = useState(2);
  const [genTwoRolloverCases, setGenTwoRolloverCases] = useState(1);
  const [genThreeRolloverCases, setGenThreeRolloverCases] = useState(1);
  const [genOneMonthlyCases, setGenOneMonthlyCases] = useState(2);
  const [genTwoMonthlyCases, setGenTwoMonthlyCases] = useState(1);
  const [genThreeMonthlyCases, setGenThreeMonthlyCases] = useState(1);

  // Calculate total cases and investments
  const totalRolloverCases = viewMode === 'agent' ? rolloverCases : rolloverCases + numAgents * avgAgentRollover;
  const totalMonthlyCases = viewMode === 'agent' ? monthlyCases : monthlyCases + numAgents * avgAgentMonthly;
  const totalCases = totalRolloverCases + totalMonthlyCases;

  // Calculate total dollar values
  const totalRolloverValue = rolloverCases * avgInitialInvestment;
  const totalMonthlyValue = monthlyCases * avgMonthlyContribution;

  // Agent totals (broker view)
  const totalAgentRolloverValue = numAgents * avgAgentRollover * agentInitialInvestment;
  const totalAgentMonthlyValue = numAgents * avgAgentMonthly * agentMonthlyContribution;

  // State for calculation results
  const [results, setResults] = useState(calculateCommissions({
    productType: 'mutual-funds',
    numCases: totalCases,
    initialInvestment: avgInitialInvestment,
    monthlyContribution: avgMonthlyContribution,
    durationYears,
    repBP: repInfo.agent.bp,
    uplineBP: viewMode === 'broker' ? repInfo.broker.bp : repInfo.agent.bp,
    viewMode,
    fundType,
    marketGrowth,
    rolloverCases: totalRolloverCases,
    monthlyCases: totalMonthlyCases
  }));

  // Recalculate results when inputs change
  useEffect(() => {
    setResults(calculateCommissions({
      productType: 'mutual-funds',
      numCases: totalCases,
      initialInvestment: viewMode === 'agent' ? avgInitialInvestment : agentInitialInvestment,
      monthlyContribution: viewMode === 'agent' ? avgMonthlyContribution : agentMonthlyContribution,
      durationYears,
      repBP: repInfo.agent.bp,
      uplineBP: viewMode === 'broker' ? repInfo.broker.bp : repInfo.agent.bp,
      viewMode,
      fundType,
      marketGrowth,
      rolloverCases: totalRolloverCases,
      monthlyCases: totalMonthlyCases,
      enableMultiGen,
      genOneAgents,
      genTwoAgents,
      genThreeAgents,
      genOneRolloverCases,
      genTwoRolloverCases,
      genThreeRolloverCases,
      genOneMonthlyCases,
      genTwoMonthlyCases,
      genThreeMonthlyCases
    }));
  }, [avgInitialInvestment, avgMonthlyContribution, durationYears, viewMode, fundType, marketGrowth, rolloverCases, monthlyCases, totalRolloverCases, totalMonthlyCases, numAgents, avgAgentRollover, avgAgentMonthly, agentInitialInvestment, agentMonthlyContribution, enableMultiGen, genOneAgents, genTwoAgents, genThreeAgents, genOneRolloverCases, genTwoRolloverCases, genThreeRolloverCases, genOneMonthlyCases, genTwoMonthlyCases, genThreeMonthlyCases]);
  return (
    <div className="w-full max-w-7xl mx-auto">
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Left Column - Input Controls */}
        <div className="space-y-6">
        <Card className="bg-white shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between pb-4">
            <CardTitle className="text-xl font-semibold">Input Controls</CardTitle>
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <Settings size={20} />
            </button>
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            {/* Settings Panel */}
            {showSettings && (
              <div className="mb-6">
                <SimulatorSettings
                  avgInitialInvestment={avgInitialInvestment}
                  setAvgInitialInvestment={setAvgInitialInvestment}
                  avgMonthlyContribution={avgMonthlyContribution}
                  setAvgMonthlyContribution={setAvgMonthlyContribution}
                  fundType={fundType}
                  setFundType={setFundType}
                  marketGrowth={marketGrowth}
                  setMarketGrowth={setMarketGrowth}
                />
              </div>
            )}

            {/* Role Toggle */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-700">View Mode</label>
              <ToggleGroup
                type="single"
                value={viewMode}
                onValueChange={value => value && setViewMode(value as 'agent' | 'broker')}
                className="justify-start w-full"
              >
                <ToggleGroupItem value="agent" className="flex items-center gap-2 flex-1">
                  <Users size={16} /> Agent View ({agentBP}% BP)
                </ToggleGroupItem>
                <ToggleGroupItem value="broker" className="flex items-center gap-2 flex-1">
                  <Users size={16} /> Broker View ({brokerBP}% BP)
                </ToggleGroupItem>
              </ToggleGroup>
            </div>

            {/* Rollover Cases */}
            <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium text-gray-700">New Rollover Cases per Month</label>
                <span className="text-sm font-semibold text-blue-600">{rolloverCases} cases</span>
              </div>
              <Slider
                value={[rolloverCases]}
                min={0}
                max={25}
                step={1}
                onValueChange={value => setRolloverCases(value[0])}
                className="py-2"
              />
              <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                Total: ({rolloverCases} × ${avgInitialInvestment.toLocaleString()}) = {formatCurrency(totalRolloverValue)}
              </div>
            </div>

            {/* Monthly Contribution Cases */}
            <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium text-gray-700">New Monthly Contribution Cases</label>
                <span className="text-sm font-semibold text-green-600">{monthlyCases} cases</span>
              </div>
              <Slider
                value={[monthlyCases]}
                min={0}
                max={25}
                step={1}
                onValueChange={value => setMonthlyCases(value[0])}
                className="py-2"
              />
              <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                Total: ({monthlyCases} × ${avgMonthlyContribution}) = {formatCurrency(totalMonthlyValue)}/month
              </div>
            </div>

            {/* Duration Years */}
            <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium text-gray-700">Duration (Years)</label>
                <span className="text-sm font-semibold text-purple-600">{durationYears} years</span>
              </div>
              <Slider
                value={[durationYears]}
                min={1}
                max={30}
                step={1}
                onValueChange={value => setDurationYears(value[0])}
                className="py-2"
              />
              <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                Years you sustain this production level
              </div>
            </div>

            {/* Trail Projection Year Toggle */}
            <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
              <label className="text-sm font-medium text-gray-700">Trail Projection Year</label>
              <ToggleGroup
                type="single"
                value={selectedTrailYear.toString()}
                onValueChange={value => value && setSelectedTrailYear(Number(value) as 5 | 10 | 20)}
                className="justify-start w-full"
              >
                <ToggleGroupItem value="5" className="flex-1">Year 5</ToggleGroupItem>
                <ToggleGroupItem value="10" className="flex-1">Year 10</ToggleGroupItem>
                <ToggleGroupItem value="20" className="flex-1">Year 20</ToggleGroupItem>
              </ToggleGroup>
              <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                Select which year's trail income to highlight in results
              </div>
            </div>

            {/* Broker View Additional Inputs */}
            {viewMode === 'broker' && (
              <div className="space-y-6 border-t-2 border-gray-200 pt-6 mt-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-800">Team Production</h3>
                  <button
                    onClick={() => setShowAgentSettings(!showAgentSettings)}
                    className="p-2 rounded-lg hover:bg-gray-100 text-xs flex items-center gap-2 transition-colors border"
                  >
                    <Settings size={14} /> Agent Settings
                  </button>
                </div>

                {/* Agent Settings Panel */}
                {showAgentSettings && (
                  <div className="border-2 border-blue-200 rounded-lg p-6 bg-blue-50 space-y-6">
                    <h4 className="text-base font-semibold text-blue-800">Agent Settings</h4>

                    {/* Agent Initial Investment */}
                    <div className="space-y-3">
                      <label htmlFor="agentInitial" className="text-sm font-medium text-gray-700">
                        Avg Initial Investment per Agent
                      </label>
                      <div className="flex items-center bg-white rounded-lg border p-2">
                        <span className="mr-2 text-gray-500">$</span>
                        <Input
                          id="agentInitial"
                          type="number"
                          value={agentInitialInvestment}
                          onChange={e => setAgentInitialInvestment(Number(e.target.value))}
                          min={1000}
                          max={500000}
                          step={1000}
                          className="border-0 focus:ring-0"
                        />
                      </div>
                    </div>

                    {/* Agent Monthly Contribution */}
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <label className="text-sm font-medium text-gray-700">Avg Monthly Contribution per Agent</label>
                        <span className="text-sm font-semibold text-blue-600">${agentMonthlyContribution}</span>
                      </div>
                      <Slider
                        value={[agentMonthlyContribution]}
                        min={50}
                        max={650}
                        step={25}
                        onValueChange={value => setAgentMonthlyContribution(value[0])}
                        className="py-2"
                      />
                    </div>
                  </div>
                )}

                {/* Number of Agents */}
                <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <label className="text-sm font-medium text-gray-700">Number of Agents</label>
                    <span className="text-sm font-semibold text-indigo-600">{numAgents}</span>
                  </div>
                  <Slider
                    value={[numAgents]}
                    min={1}
                    max={20}
                    step={1}
                    onValueChange={value => setNumAgents(value[0])}
                    className="py-2"
                  />
                </div>

                {/* Avg Agent Rollover Cases */}
                <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <label className="text-sm font-medium text-gray-700">Avg Rollover Cases per Agent</label>
                    <span className="text-sm font-semibold text-blue-600">{avgAgentRollover} cases</span>
                  </div>
                  <Slider
                    value={[avgAgentRollover]}
                    min={0}
                    max={15}
                    step={1}
                    onValueChange={value => setAvgAgentRollover(value[0])}
                    className="py-2"
                  />
                  <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                    Total: ({numAgents} agents × {avgAgentRollover} cases × ${agentInitialInvestment.toLocaleString()}) = {formatCurrency(totalAgentRolloverValue)}
                  </div>
                </div>

                {/* Avg Agent Monthly Cases */}
                <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <label className="text-sm font-medium text-gray-700">Avg Monthly Cases per Agent</label>
                    <span className="text-sm font-semibold text-green-600">{avgAgentMonthly} cases</span>
                  </div>
                  <Slider
                    value={[avgAgentMonthly]}
                    min={0}
                    max={15}
                    step={1}
                    onValueChange={value => setAvgAgentMonthly(value[0])}
                    className="py-2"
                  />
                  <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                    Total: ({numAgents} agents × {avgAgentMonthly} cases × ${agentMonthlyContribution}) = {formatCurrency(totalAgentMonthlyValue)}/month
                  </div>
                </div>

                {/* Multi-Generation Override Settings */}
                <MultiGenSettings enableMultiGen={enableMultiGen} setEnableMultiGen={setEnableMultiGen} genOneAgents={genOneAgents} setGenOneAgents={setGenOneAgents} genTwoAgents={genTwoAgents} setGenTwoAgents={setGenTwoAgents} genThreeAgents={genThreeAgents} setGenThreeAgents={setGenThreeAgents} genOneRolloverCases={genOneRolloverCases} setGenOneRolloverCases={setGenOneRolloverCases} genTwoRolloverCases={genTwoRolloverCases} setGenTwoRolloverCases={setGenTwoRolloverCases} genThreeRolloverCases={genThreeRolloverCases} setGenThreeRolloverCases={setGenThreeRolloverCases} genOneMonthlyCases={genOneMonthlyCases} setGenOneMonthlyCases={setGenOneMonthlyCases} genTwoMonthlyCases={genTwoMonthlyCases} setGenTwoMonthlyCases={setGenTwoMonthlyCases} genThreeMonthlyCases={genThreeMonthlyCases} setGenThreeMonthlyCases={setGenThreeMonthlyCases} avgInitialInvestment={avgInitialInvestment} avgMonthlyContribution={avgMonthlyContribution} />

                {/* Total Production Summary */}
                <div className="mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border-2 border-green-200">
                  <h4 className="text-base font-semibold mb-4 text-green-800">Total Production Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="bg-white p-3 rounded-lg border">
                      <p className="text-gray-600 text-xs uppercase tracking-wide">Your Rollover Cases:</p>
                      <p className="font-semibold text-lg text-blue-600">{rolloverCases} ({formatCurrency(totalRolloverValue)})</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <p className="text-gray-600 text-xs uppercase tracking-wide">Team Rollover Cases:</p>
                      <p className="font-semibold text-lg text-indigo-600">{numAgents * avgAgentRollover} ({formatCurrency(totalAgentRolloverValue)})</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <p className="text-gray-600 text-xs uppercase tracking-wide">Your Monthly Cases:</p>
                      <p className="font-semibold text-lg text-green-600">{monthlyCases} ({formatCurrency(totalMonthlyValue)}/mo)</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <p className="text-gray-600 text-xs uppercase tracking-wide">Team Monthly Cases:</p>
                      <p className="font-semibold text-lg text-emerald-600">{numAgents * avgAgentMonthly} ({formatCurrency(totalAgentMonthlyValue)}/mo)</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        </div>

        {/* Right Column - Results Section */}
        <div className="space-y-6">
          <SimulatorResults
            results={results}
            viewMode={viewMode}
            fundType={fundType}
            marketGrowth={marketGrowth}
            durationYears={durationYears}
            enableMultiGen={enableMultiGen}
            selectedTrailYear={selectedTrailYear}
          />
        </div>
      </div>
    </div>
  );
};
export default SimulatorForm;