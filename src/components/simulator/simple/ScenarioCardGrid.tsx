
import React from 'react';
import { SimpleScenarioResults, SimpleSimulationInputs } from '@/types/simpleTypes';
import { ScenarioSettings } from '@/types/scenarioSettings';
import SelfEmployedCard from './SelfEmployedCard';
import SelfEmployedTeamCard from './SelfEmployedTeamCard';
import BusinessOwnerCard from './BusinessOwnerCard';
import BranchOfficePassiveCardNew from './BranchOfficePassiveCardNew';
import { useColorTheme } from '@/contexts/ThemeContext';

interface ScenarioCardGridProps {
  results: SimpleScenarioResults;
  inputs: SimpleSimulationInputs;
  scenarioSettings: ScenarioSettings;
  onInputChange: (name: string, value: number) => void;
}

const ScenarioCardGrid: React.FC<ScenarioCardGridProps> = ({ results, inputs, scenarioSettings, onInputChange }) => {
  const { colorTheme } = useColorTheme();

  return (
    <div className="grid grid-cols-1 gap-4">
      {/* Self Employed */}
      <SelfEmployedCard
        title="Self Employed"
        subtitle="You own Your Job"
        colorTheme={colorTheme}
        incomeData={{
          directEffort: results.selfEmployed.directEffort,
          recurringPac: results.selfEmployed.recurringPac,
          trailIncome: results.selfEmployed.trailIncome,
          total: results.selfEmployed.total
        }}
        simulatorInputs={{...inputs, aum: results.aum, scenarioSettings}}
      />

      {/* SE + Team of Licensed Reps */}
      <SelfEmployedTeamCard
        title="Business Owner"
        subtitle="You + Team of Licensed Reps"
        colorTheme={colorTheme}
        incomeData={{
          directEffort: results.selfEmployedTeam.directEffort,
          override: results.selfEmployedTeam.teamOverride,
          recurringPac: results.selfEmployedTeam.recurringPac,
          trailIncome: results.selfEmployedTeam.trailIncome,
          total: results.selfEmployedTeam.total
        }}
        numRLs={inputs.numRLs}
        onNumRLsChange={(value) => onInputChange('numRLs', value)}
        simulatorInputs={{...inputs, aum: results.aum, scenarioSettings}}
      />

      {/* BO with Expansion Offices + RLs */}
      <BusinessOwnerCard
        title="Expansion Offices"
        subtitle="BO + Licensed Reps Who Opened Their Own Offices"
        colorTheme={colorTheme}
        incomeData={{
          directEffort: results.businessOwner.personalOffice.directEffort,
          override: results.businessOwner.personalOffice.override,
          recurringPac: results.businessOwner.personalOffice.recurringPac,
          trailIncome: results.businessOwner.personalOffice.trailIncome,
          agencyOverride: results.businessOwner.agencyOverrides.overrideIncome,
          agencyRecurringPac: results.businessOwner.agencyOverrides.recurringPacIncome,
          agencyTrail: results.businessOwner.agencyOverrides.trailIncome,
          total: results.businessOwner.total
        }}
        numAgencies={inputs.businessOwnerAgencies}
        onNumAgenciesChange={(value) => onInputChange('businessOwnerAgencies', value)}
        numRLsPerAgency={inputs.businessOwnerRLsPerAgency}
        onNumRLsPerAgencyChange={(value) => onInputChange('businessOwnerRLsPerAgency', value)}
        simulatorInputs={{
          ...inputs,
          aum: results.aum,
          scenarioSettings,
          // Use Business Owner expansion office settings for this card
          numAgencies: inputs.businessOwnerAgencies,
          numRLsPerAgency: inputs.businessOwnerRLsPerAgency
        }}
      />

      {/* Expansion Offices Passive (Owns Expansion Offices, No Work) */}
      <BranchOfficePassiveCardNew
        title="Expansion Offices Passive"
        subtitle="Your Licensed Reps Run Their Own Offices"
        colorTheme={colorTheme}
        incomeData={{
          directEffort: 0, // Passive owner has no direct effort
          recurringPac: results.passiveOwner.recurringPac,
          trailIncome: results.passiveOwner.trailIncome,
          agencyOverride: results.passiveOwner.agencyOverrides.overrideIncome,
          agencyRecurringPac: results.passiveOwner.agencyOverrides.recurringPacIncome,
          agencyTrail: results.passiveOwner.agencyOverrides.trailIncome,
          total: results.passiveOwner.total
        }}
        numAgencies={inputs.branchOfficeAgencies}
        onNumAgenciesChange={(value) => onInputChange('branchOfficeAgencies', value)}
        numRLsPerAgency={inputs.branchOfficeRLsPerAgency}
        onNumRLsPerAgencyChange={(value) => onInputChange('branchOfficeRLsPerAgency', value)}
        simulatorInputs={{
          ...inputs,
          aum: results.aum,
          scenarioSettings,
          // Use Branch Office expansion office settings for this card
          numAgencies: inputs.branchOfficeAgencies,
          numRLsPerAgency: inputs.branchOfficeRLsPerAgency
        }}
      />
    </div>
  );
};

export default ScenarioCardGrid;
