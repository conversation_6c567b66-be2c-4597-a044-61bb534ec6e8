import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { motion, AnimatePresence } from 'framer-motion';
import { formatCurrency } from '@/utils/formatting';
import { Briefcase, ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getCDRRate } from '@/data/CDRBreakpoints';

// Import our specialized card components with "Show our work" feature
import DirectEffortCard from './cards/DirectEffortCardNew';
import RecurringPacCard from './cards/RecurringPacCardNew';
import TrailIncomeCard from './cards/TrailIncomeCardNew';


interface SimulatorInputs {
  initialInvestment: number;
  numRolloverCases: number;
  pacPerCase: number;
  numPacCases: number;
  projectionYears: number;
  marketGrowth: number;
  aum?: {
    selfEmployed?: {
      projectedAUM: number;
    };
  };
}

interface SelfEmployedCardProps {
  title: string;
  subtitle: string;
  colorTheme: string;
  incomeData: {
    directEffort?: number;
    recurringPac?: number;
    trailIncome?: number;
    total: number;
  };
  simulatorInputs?: SimulatorInputs;
}

/**
 * Self Employed card using reusable card components
 * This maintains the exact same visual appearance as the current implementation
 */
const SelfEmployedCard: React.FC<SelfEmployedCardProps> = ({
  title,
  subtitle,
  colorTheme,
  incomeData,
  simulatorInputs
}) => {
  const [isExpanded, setIsExpanded] = useState(false);



  return (
    <Card className="shadow-xl overflow-hidden border-0 transition-all duration-300 hover:shadow-2xl">
      <CardHeader className="p-5 flex flex-row items-center justify-between bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <div className="flex items-center gap-2">
          <Briefcase size={20} />
          <div>
            <CardTitle className="text-lg md:text-xl">{title}</CardTitle>
            <p className="text-xs md:text-sm opacity-80">{subtitle}</p>
          </div>
        </div>

      </CardHeader>
      <CardContent className="p-6 bg-gray-50/50">
        {/* Total Monthly Income */}
        <div className={`bg-white rounded-lg shadow-lg border transition-all duration-300 hover:shadow-xl ${isExpanded ? 'rounded-b-none' : ''}`}>
          <div
            className="flex justify-between items-center py-4 px-5 cursor-pointer"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <h3 className="text-sm uppercase text-slate-700 font-semibold tracking-wide">TOTAL MONTHLY INCOME</h3>
            <div className="flex items-center">
              <span className="text-xl md:text-2xl font-bold mr-2 text-slate-900">{formatCurrency(incomeData.total)}</span>
              <div className="bg-gray-100 rounded-full p-1">
                {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </div>
            </div>
          </div>
        </div>
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white border border-t-0 rounded-b-lg shadow-lg p-5 -mt-px"
            >
              <div className="space-y-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-slate-800">The Breakdown:</h3>
                  <span className="text-xl font-bold text-slate-900">{formatCurrency(incomeData.total)}</span>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                  {/* Direct Effort Income */}
                  <DirectEffortCard
                    value={incomeData.directEffort || 0}
                    personalProduction={simulatorInputs ? {
                      initialInvestment: simulatorInputs.initialInvestment,
                      numRolloverCases: simulatorInputs.numRolloverCases,
                      pacPerCase: simulatorInputs.pacPerCase,
                      numPacCases: simulatorInputs.numPacCases,
                      commissionRate: 0.425,
                      rolloverCdrRate: getCDRRate(simulatorInputs.initialInvestment),
                      pacCdrRate: getCDRRate(simulatorInputs.pacPerCase)
                    } : undefined}
                  />

                  {/* Recurring PAC Income */}
                  <RecurringPacCard
                    value={incomeData.recurringPac || 0}
                    pacData={simulatorInputs ? {
                      pacPerCase: simulatorInputs.pacPerCase,
                      numPacCases: simulatorInputs.numPacCases,
                      projectionYears: simulatorInputs.projectionYears,
                      commissionRate: 0.425,
                      cdrRate: getCDRRate(simulatorInputs.pacPerCase)
                    } : undefined}
                  />

                  {/* Trail Income */}
                  <TrailIncomeCard
                    value={incomeData.trailIncome || 0}
                    trailData={simulatorInputs ? {
                      projectedAUM: simulatorInputs.aum?.selfEmployed?.projectedAUM || 0,
                      trailRate: 0.0025,
                      marketGrowth: simulatorInputs.marketGrowth,
                      projectionYears: simulatorInputs.projectionYears
                    } : undefined}
                  />
                </div>
              </div>

              <div className="text-center text-xs text-slate-600 mt-4">
                Click on any card above to see calculation details
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>


    </Card>
  );
};

export default SelfEmployedCard;
