import React from 'react';
import ShowWorkCard from './ShowWorkCard';
import { Briefcase } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface DirectEffortCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  personalProduction?: {
    initialInvestment: number;
    numRolloverCases: number;
    commissionRate?: number;
    cdrRate?: number;
  };
}

const DirectEffortCard: React.FC<DirectEffortCardNewProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  personalProduction
}) => {
  // Generate calculation steps
  const generateCalculations = () => {
    if (!personalProduction) {
      return [
        { label: "No calculation data available", value: "N/A" }
      ];
    }

    const { 
      initialInvestment, 
      numRolloverCases, 
      commissionRate = 0.425,
      cdrRate = 0.055 
    } = personalProduction;

    const totalRollover = initialInvestment * numRolloverCases;
    const grossCommission = totalRollover * cdrRate;
    const finalCommission = grossCommission * commissionRate;

    return [
      {
        label: "Number of Cases",
        value: `${numRolloverCases} cases`,
        isSubCalculation: false
      },
      {
        label: "Investment per Case",
        value: formatCurrency(initialInvestment),
        isSubCalculation: false
      },
      {
        label: "Total Investment",
        value: `${numRolloverCases} × ${formatCurrency(initialInvestment)}`,
        isSubCalculation: true
      },
      {
        label: "Total Investment Amount",
        value: totalRollover,
        isSubCalculation: false
      },
      {
        label: "CDR Rate",
        value: `${(cdrRate * 100).toFixed(1)}%`,
        isSubCalculation: false
      },
      {
        label: "Gross Commission",
        value: `${formatCurrency(totalRollover)} × ${(cdrRate * 100).toFixed(1)}%`,
        isSubCalculation: true
      },
      {
        label: "Gross Commission Amount",
        value: grossCommission,
        isSubCalculation: false
      },
      {
        label: "Commission Rate",
        value: `${(commissionRate * 100).toFixed(1)}%`,
        isSubCalculation: false
      },
      {
        label: "Final Calculation",
        value: `${formatCurrency(grossCommission)} × ${(commissionRate * 100).toFixed(1)}%`,
        isSubCalculation: true
      },
      {
        label: "Direct Effort Income",
        value: finalCommission,
        isResult: true
      }
    ];
  };

  return (
    <ShowWorkCard
      title="Direct Effort Income"
      subtitle="This month's new business"
      value={value}
      icon={Briefcase}
      iconText="Personal Production"
      colorScheme="blue"
      className={className}
      calculations={generateCalculations()}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default DirectEffortCard;
