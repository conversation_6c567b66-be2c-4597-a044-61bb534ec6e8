import React from 'react';
import ShowWorkCard from './ShowWorkCard';
import { Briefcase } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface DirectEffortCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  personalProduction?: {
    initialInvestment: number;
    numRolloverCases: number;
    pacPerCase?: number;
    numPacCases?: number;
    commissionRate?: number;
    rolloverCdrRate?: number;
    pacCdrRate?: number;
  };
}

const DirectEffortCard: React.FC<DirectEffortCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  personalProduction
}) => {
  // Generate calculation steps
  const generateCalculations = () => {
    if (!personalProduction) {
      return [
        { label: "No calculation data available", value: "N/A" }
      ];
    }

    const {
      initialInvestment,
      numRolloverCases,
      pacPerCase = 0,
      numPacCases = 0,
      commissionRate = 0.425,
      rolloverCdrRate = 0.055,
      pacCdrRate = 0.05
    } = personalProduction;

    // Calculate rollover component
    const totalRollover = initialInvestment * numRolloverCases;
    const rolloverGrossCommission = totalRollover * rolloverCdrRate;
    const rolloverFinalCommission = rolloverGrossCommission * commissionRate;

    // Calculate PAC component
    const totalPacContribution = pacPerCase * numPacCases;
    const pacGrossCommission = totalPacContribution * pacCdrRate;
    const pacFinalCommission = pacGrossCommission * commissionRate;

    // Total direct effort income
    const finalCommission = rolloverFinalCommission + pacFinalCommission;

    const calculations = [
      // Rollover Component
      {
        label: "Rollover Cases",
        value: `${numRolloverCases} cases`,
        isSubCalculation: false
      },
      {
        label: "Investment per Rollover Case",
        value: formatCurrency(initialInvestment),
        isSubCalculation: false
      },
      {
        label: "Total Rollover Investment",
        value: `${numRolloverCases} × ${formatCurrency(initialInvestment)}`,
        isSubCalculation: true
      },
      {
        label: "Rollover Investment Amount",
        value: formatCurrency(totalRollover),
        isSubCalculation: false
      },
      {
        label: "Rollover CDR Rate",
        value: `${(rolloverCdrRate * 100).toFixed(1)}%`,
        isSubCalculation: false
      },
      {
        label: "Rollover Gross Commission",
        value: `${formatCurrency(totalRollover)} × ${(rolloverCdrRate * 100).toFixed(1)}%`,
        isSubCalculation: true
      },
      {
        label: "Rollover Commission Amount",
        value: formatCurrency(rolloverGrossCommission),
        isSubCalculation: false
      }
    ];

    // Add PAC component if there are PAC cases
    if (numPacCases > 0) {
      calculations.push(
        {
          label: "PAC Cases",
          value: `${numPacCases} cases`,
          isSubCalculation: false
        },
        {
          label: "PAC per Case",
          value: formatCurrency(pacPerCase),
          isSubCalculation: false
        },
        {
          label: "Total PAC Contribution",
          value: `${numPacCases} × ${formatCurrency(pacPerCase)}`,
          isSubCalculation: true
        },
        {
          label: "PAC Contribution Amount",
          value: formatCurrency(totalPacContribution),
          isSubCalculation: false
        },
        {
          label: "PAC CDR Rate",
          value: `${(pacCdrRate * 100).toFixed(1)}%`,
          isSubCalculation: false
        },
        {
          label: "PAC Gross Commission",
          value: `${formatCurrency(totalPacContribution)} × ${(pacCdrRate * 100).toFixed(1)}%`,
          isSubCalculation: true
        },
        {
          label: "PAC Commission Amount",
          value: formatCurrency(pacGrossCommission),
          isSubCalculation: false
        }
      );
    }

    // Add final calculation
    calculations.push(
      {
        label: "Commission Rate",
        value: `${(commissionRate * 100).toFixed(1)}%`,
        isSubCalculation: false
      },
      {
        label: "Total Gross Commission",
        value: formatCurrency(rolloverGrossCommission + pacGrossCommission),
        isSubCalculation: false
      },
      {
        label: "Final Calculation",
        value: `${formatCurrency(rolloverGrossCommission + pacGrossCommission)} × ${(commissionRate * 100).toFixed(1)}%`,
        isSubCalculation: true
      },
      {
        label: "Direct Effort Income",
        value: formatCurrency(finalCommission),
        isResult: true
      }
    );

    return calculations;
  };

  return (
    <ShowWorkCard
      title="Direct Effort Income"
      subtitle="This month's new business"
      value={value}
      icon={Briefcase}
      iconText="Personal Production"
      colorScheme="blue"
      className={className}
      calculations={generateCalculations()}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default DirectEffortCard;
