import React from 'react';
import ShowWorkCard from './ShowWorkCard';
import { Building2 } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface AgencyNetworkCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  agencyData?: {
    numAgencies?: number;
    numRLsPerAgency?: number;
    agencyProduction?: number;
    overrideRate?: number;
    commissionRate?: number;
    cdrRate?: number;
    agencyStartYear?: number;
    projectionYears?: number;
  };
  incomeType?: 'override' | 'pac' | 'trail';
}

const AgencyNetworkCard: React.FC<AgencyNetworkCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  agencyData,
  incomeType = 'override'
}) => {
  // Generate calculation steps based on income type
  const generateCalculations = () => {
    if (!agencyData) {
      return [
        { label: "No calculation data available", value: "N/A" }
      ];
    }

    const { 
      numAgencies = 2,
      numRLsPerAgency = 3,
      agencyProduction = 25000,
      overrideRate = 0.15, // 15% agency override rate
      commissionRate = 0.62,
      cdrRate = 0.055,
      agencyStartYear = 5,
      projectionYears = 10
    } = agencyData;

    const totalRLs = numAgencies * numRLsPerAgency;
    const totalAgencyProduction = totalRLs * agencyProduction;
    const grossAgencyCommission = totalAgencyProduction * cdrRate;
    const agencyCommission = grossAgencyCommission * commissionRate;
    const finalOverride = agencyCommission * overrideRate;

    const calculations = [
      { label: "Number of Expansion Offices", value: `${numAgencies} expansion offices` },
      { label: "Licensed Reps per Expansion Office", value: `${numRLsPerAgency} Licensed Reps` },
      { label: "Total Licensed Reps in Network", value: `${numAgencies} × ${numRLsPerAgency}`, isSubCalculation: true },
      { label: "Total Licensed Reps", value: `${totalRLs} Licensed Reps` },
      { label: "Production per Licensed Rep", value: formatCurrency(agencyProduction) },
      { label: "Total Expansion Office Production", value: `${totalRLs} × ${formatCurrency(agencyProduction)}`, isSubCalculation: true },
      { label: "Expansion Office Production Amount", value: formatCurrency(totalAgencyProduction) },
      { label: "CDR Rate", value: `${(cdrRate * 100).toFixed(1)}%` },
      { label: "Gross Expansion Office Commission", value: `${formatCurrency(totalAgencyProduction)} × ${(cdrRate * 100).toFixed(1)}%`, isSubCalculation: true },
      { label: "Gross Commission Amount", value: formatCurrency(grossAgencyCommission) },
      { label: "Expansion Office Commission Rate", value: `${(commissionRate * 100).toFixed(1)}%` },
      { label: "Expansion Office Commission", value: `${formatCurrency(grossAgencyCommission)} × ${(commissionRate * 100).toFixed(1)}%`, isSubCalculation: true },
      { label: "Expansion Office Commission Amount", value: formatCurrency(agencyCommission) },
      { label: "Expansion Office Override Rate", value: `${(overrideRate * 100).toFixed(1)}%` },
      { label: "Override Calculation", value: `${formatCurrency(agencyCommission)} × ${(overrideRate * 100).toFixed(1)}%`, isSubCalculation: true },
      { label: "Expansion Office Network Income", value: formatCurrency(finalOverride), isResult: true }
    ];

    if (agencyStartYear > 0) {
      calculations.unshift(
        { label: "Expansion Office Start Year", value: `Year ${agencyStartYear + 1}` },
        { label: "Active Expansion Office Years", value: `${Math.max(0, projectionYears - agencyStartYear)} years` }
      );
    }

    return calculations;
  };

  const getCardTitle = () => {
    switch (incomeType) {
      case 'pac': return 'Expansion Office PAC Income';
      case 'trail': return 'Expansion Office Trail Income';
      default: return 'Expansion Office Network Income';
    }
  };

  const getCardSubtitle = () => {
    switch (incomeType) {
      case 'pac': return 'PAC from expansion office network';
      case 'trail': return 'Trail from expansion office AUM';
      default: return 'Override from expansion office network';
    }
  };

  return (
    <ShowWorkCard
      title={getCardTitle()}
      subtitle={getCardSubtitle()}
      value={value}
      icon={Building2}
      iconText="Expansion Office Network"
      colorScheme="red"
      className={className}
      calculations={generateCalculations()}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default AgencyNetworkCard;
