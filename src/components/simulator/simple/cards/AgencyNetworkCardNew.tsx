import React from 'react';
import ShowWorkCard from './ShowWorkCard';
import { Building2 } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface AgencyNetworkCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  agencyData?: {
    numAgencies?: number;
    numRLsPerAgency?: number;
    // Production components
    initialInvestment?: number;
    numRolloverCases?: number;
    pacPerCase?: number;
    numPacCases?: number;
    // Legacy single production value (for backwards compatibility)
    agencyProduction?: number;
    overrideRate?: number;
    commissionRate?: number;
    cdrRate?: number;
    agencyStartYear?: number;
    projectionYears?: number;
    marketGrowth?: number;
  };
  incomeType?: 'override' | 'pac' | 'trail';
}

const AgencyNetworkCard: React.FC<AgencyNetworkCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  agencyData,
  incomeType = 'override'
}) => {
  // Generate calculation steps based on income type
  const generateCalculations = () => {
    if (!agencyData) {
      return [
        { label: "No calculation data available", value: "N/A" }
      ];
    }

    const {
      numAgencies = 2,
      numRLsPerAgency = 3,
      initialInvestment = 25000,
      numRolloverCases = 5, // Match default from Income Simulator Settings
      pacPerCase = 250, // Match default from Income Simulator Settings
      numPacCases = 5,
      agencyProduction = 25000, // Fallback for backwards compatibility
      overrideRate = 0.15, // 15% agency override rate
      commissionRate = 0.62,
      cdrRate = 0.055,
      agencyStartYear = 5,
      projectionYears = 10,
      marketGrowth = 0.10
    } = agencyData;

    const totalRLs = numAgencies * numRLsPerAgency;

    // Calculate total production per person based on income type
    let rvpTotalProduction = 0;
    let rlTotalProduction = 0;

    if (incomeType === 'override') {
      // For override calculations, use rollover cases only
      rvpTotalProduction = initialInvestment * numRolloverCases; // RVP at 100%
      rlTotalProduction = (initialInvestment * numRolloverCases) * 0.5; // RL at 50%
    } else if (incomeType === 'pac') {
      // For PAC calculations, use PAC cases only
      rvpTotalProduction = pacPerCase * numPacCases; // RVP at 100%
      rlTotalProduction = (pacPerCase * numPacCases) * 0.5; // RL at 50%
    } else if (incomeType === 'trail') {
      // For trail calculations, calculate network AUM with proper compound growth
      const yearsOfGrowth = Math.max(0, projectionYears - agencyStartYear);
      const monthsOfGrowth = yearsOfGrowth * 12;
      const monthlyGrowth = Math.pow(1 + marketGrowth, 1/12) - 1;

      // Calculate AUM per office using the same logic as personal AUM calculation
      let aumPerOffice = 0;
      const monthlyRolloverInvestment = initialInvestment * numRolloverCases;
      const monthlyPacContribution = pacPerCase * numPacCases;

      // Accumulate AUM with monthly contributions and growth
      for (let month = 0; month < monthsOfGrowth; month++) {
        aumPerOffice += monthlyRolloverInvestment + monthlyPacContribution;
        aumPerOffice = aumPerOffice * (1 + monthlyGrowth);
      }

      rvpTotalProduction = aumPerOffice; // RVP contributes full AUM
      rlTotalProduction = aumPerOffice * 0.5; // Each RL contributes 50% of RVP AUM
    } else {
      // For legacy, use the combined production or fallback
      rvpTotalProduction = agencyProduction;
      rlTotalProduction = agencyProduction * 0.5;
    }

    const productionPerAgency = rvpTotalProduction + (numRLsPerAgency * rlTotalProduction);
    const totalAgencyProduction = numAgencies * productionPerAgency;
    const grossAgencyCommission = totalAgencyProduction * cdrRate;
    const agencyCommission = grossAgencyCommission * commissionRate;

    // For trail income, we don't apply override rate - it's direct trail income
    // For other income types, we apply the override rate
    const finalOverride = incomeType === 'trail' ? agencyCommission / 12 : agencyCommission * overrideRate;

    // Enhanced calculations showing case-based breakdown
    const calculations = [
      { label: "Number of Expansion Offices", value: `${numAgencies} expansion offices` },
      { label: "Licensed Reps per Expansion Office", value: `${numRLsPerAgency} Licensed Reps` },
      { label: "Total Licensed Reps in Network", value: `${numAgencies} × ${numRLsPerAgency}`, isSubCalculation: true },
      { label: "Total Licensed Reps", value: `${totalRLs} Licensed Reps` },
    ];

    // Add production breakdown based on income type
    if (incomeType === 'override') {
      calculations.push(
        { label: "Initial Investment per Case", value: formatCurrency(initialInvestment) },
        { label: "Rollover Cases per RVP", value: `${numRolloverCases} cases` },
        { label: "RVP Production per Office", value: `${formatCurrency(initialInvestment)} × ${numRolloverCases}`, isSubCalculation: true },
        { label: "RVP Total Production", value: formatCurrency(rvpTotalProduction) },
        { label: "RL Production (50% of RVP)", value: formatCurrency(rlTotalProduction) },
        { label: "RL Production per Office", value: `${numRLsPerAgency} × ${formatCurrency(rlTotalProduction)}`, isSubCalculation: true },
        { label: "Total per Office", value: `${formatCurrency(rvpTotalProduction)} + ${formatCurrency(numRLsPerAgency * rlTotalProduction)}`, isSubCalculation: true },
        { label: "Production per Office", value: formatCurrency(productionPerAgency) }
      );
    } else if (incomeType === 'pac') {
      calculations.push(
        { label: "PAC per Case", value: formatCurrency(pacPerCase) },
        { label: "PAC Cases per RVP", value: `${numPacCases} cases` },
        { label: "RVP Production per Office", value: `${formatCurrency(pacPerCase)} × ${numPacCases}`, isSubCalculation: true },
        { label: "RVP Total Production", value: formatCurrency(rvpTotalProduction) },
        { label: "RL Production (50% of RVP)", value: formatCurrency(rlTotalProduction) },
        { label: "RL Production per Office", value: `${numRLsPerAgency} × ${formatCurrency(rlTotalProduction)}`, isSubCalculation: true },
        { label: "Total per Office", value: `${formatCurrency(rvpTotalProduction)} + ${formatCurrency(numRLsPerAgency * rlTotalProduction)}`, isSubCalculation: true },
        { label: "Production per Office", value: formatCurrency(productionPerAgency) }
      );
    } else if (incomeType === 'trail') {
      const yearsOfGrowth = Math.max(0, projectionYears - agencyStartYear);
      calculations.push(
        { label: `Projected Network AUM (Year ${projectionYears})`, value: formatCurrency(totalAgencyProduction) },
        { label: "Annual Trail Rate", value: `${(cdrRate * 100).toFixed(2)}%` },
        { label: "Annual Trail Income", value: `${formatCurrency(totalAgencyProduction)} × ${(cdrRate * 100).toFixed(2)}%`, isSubCalculation: true },
        { label: "Annual Trail Amount", value: formatCurrency(grossAgencyCommission) },
        { label: "Commission Rate", value: `${(commissionRate * 100).toFixed(1)}%` },
        { label: "Final Calculation", value: `${formatCurrency(grossAgencyCommission)} × ${(commissionRate * 100).toFixed(1)}%`, isSubCalculation: true },
        { label: "Annual Trail Income", value: formatCurrency(agencyCommission) },
        { label: "Monthly Trail Calculation", value: `${formatCurrency(agencyCommission)} ÷ 12 months`, isSubCalculation: true },
        { label: "Expansion Office Start Year", value: `Year ${agencyStartYear + 1}` },
        { label: "Growth Note", value: "Continuous new investments included" }
      );
    } else {
      calculations.push(
        { label: "RVP Production per Office", value: formatCurrency(rvpTotalProduction) },
        { label: "RL Production (50% of RVP)", value: formatCurrency(rlTotalProduction) },
        { label: "RL Production per Office", value: `${numRLsPerAgency} × ${formatCurrency(rlTotalProduction)}`, isSubCalculation: true },
        { label: "Production per Office", value: formatCurrency(productionPerAgency) }
      );
    }

    if (incomeType === 'trail') {
      calculations.push(
        { label: "Monthly Trail Income", value: formatCurrency(finalOverride) },
        { label: "Final Result", value: formatCurrency(finalOverride) }
      );
    } else {
      calculations.push(
        { label: "Total Expansion Office Production", value: `${numAgencies} × ${formatCurrency(productionPerAgency)}`, isSubCalculation: true },
        { label: "Expansion Office Production Amount", value: formatCurrency(totalAgencyProduction) },
        { label: "CDR Rate", value: `${(cdrRate * 100).toFixed(1)}%` },
        { label: "Gross Expansion Office Commission", value: `${formatCurrency(totalAgencyProduction)} × ${(cdrRate * 100).toFixed(1)}%`, isSubCalculation: true },
        { label: "Gross Commission Amount", value: formatCurrency(grossAgencyCommission) },
        { label: "Expansion Office Commission Rate", value: `${(commissionRate * 100).toFixed(1)}%` },
        { label: "Expansion Office Commission", value: `${formatCurrency(grossAgencyCommission)} × ${(commissionRate * 100).toFixed(1)}%`, isSubCalculation: true },
        { label: "Expansion Office Commission Amount", value: formatCurrency(agencyCommission) },
        { label: "Expansion Office Override Rate", value: `${(overrideRate * 100).toFixed(1)}%` },
        { label: "Override Calculation", value: `${formatCurrency(agencyCommission)} × ${(overrideRate * 100).toFixed(1)}%`, isSubCalculation: true },
        { label: "Expansion Office Network Income", value: formatCurrency(finalOverride) }
      );
    }

    if (agencyStartYear > 0) {
      calculations.unshift(
        { label: "Expansion Office Start Year", value: `Year ${agencyStartYear + 1}` },
        { label: "Active Expansion Office Years", value: `${Math.max(0, projectionYears - agencyStartYear)} years` }
      );
    }

    return calculations;
  };

  const getCardTitle = () => {
    switch (incomeType) {
      case 'pac': return 'Expansion Office PAC Income';
      case 'trail': return 'Expansion Office Trail Income';
      default: return 'Expansion Office Network Income';
    }
  };

  const getCardSubtitle = () => {
    switch (incomeType) {
      case 'pac': return 'PAC from expansion office network';
      case 'trail': return 'Trail from expansion office AUM';
      default: return 'Override from expansion office network';
    }
  };

  return (
    <ShowWorkCard
      title={getCardTitle()}
      subtitle={getCardSubtitle()}
      value={value}
      icon={Building2}
      iconText="Expansion Office Network"
      colorScheme="red"
      className={className}
      calculations={generateCalculations()}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default AgencyNetworkCard;
