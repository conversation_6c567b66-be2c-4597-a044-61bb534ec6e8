import React from 'react';
import ShowWorkCard from './ShowWorkCard';
import { TrendingUp } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface TrailIncomeCardNewProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  trailData?: {
    projectedAUM: number;
    trailRate?: number;
    marketGrowth?: number;
    projectionYears: number;
  };
}

const TrailIncomeCardNew: React.FC<TrailIncomeCardNewProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  trailData
}) => {
  // Generate calculation steps
  const generateCalculations = () => {
    if (!trailData) {
      return [
        { label: "No calculation data available", value: "N/A" }
      ];
    }

    const { 
      projectedAUM, 
      trailRate = 0.0025, // 0.25% annual trail rate
      marketGrowth = 0.10,
      projectionYears
    } = trailData;

    const annualTrailIncome = projectedAUM * trailRate;
    const monthlyTrailIncome = annualTrailIncome / 12;

    return [
      {
        label: "Projected AUM (Year " + projectionYears + ")",
        value: projectedAUM,
        isSubCalculation: false
      },
      {
        label: "Annual Trail Rate",
        value: `${(trailRate * 100).toFixed(2)}%`,
        isSubCalculation: false
      },
      {
        label: "Annual Trail Income",
        value: `${formatCurrency(projectedAUM)} × ${(trailRate * 100).toFixed(2)}%`,
        isSubCalculation: true
      },
      {
        label: "Annual Trail Amount",
        value: annualTrailIncome,
        isSubCalculation: false
      },
      {
        label: "Monthly Trail Calculation",
        value: `${formatCurrency(annualTrailIncome)} ÷ 12 months`,
        isSubCalculation: true
      },
      {
        label: "Market Growth Rate",
        value: `${(marketGrowth * 100).toFixed(1)}% annually`,
        isSubCalculation: false
      },
      {
        label: "Growth Note",
        value: "Continuous new investments included",
        isSubCalculation: true
      },
      {
        label: "Monthly Trail Income",
        value: monthlyTrailIncome,
        isResult: true
      }
    ];
  };

  return (
    <ShowWorkCard
      title="Trail Income"
      subtitle="From personal AUM"
      value={value}
      icon={TrendingUp}
      iconText="Assets Under Management"
      colorScheme="purple"
      className={className}
      calculations={generateCalculations()}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default TrailIncomeCardNew;
