import React from 'react';
import { LucideIcon, Edit } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';
import { cn } from '@/lib/utils';

interface IncomeCardProps {
  title: string;
  subtitle: string;
  value: number;
  icon: LucideIcon;
  iconText: string;
  colorScheme: 'blue' | 'green' | 'purple' | 'red' | 'indigo';
  className?: string;
  children?: React.ReactNode;
  onEdit?: () => void;
  showEditButton?: boolean;
}

/**
 * A reusable card component for displaying different types of income
 * This component maintains the exact same visual appearance as the current cards
 */
const IncomeCard: React.FC<IncomeCardProps> = ({
  title,
  subtitle,
  value,
  icon: Icon,
  iconText,
  colorScheme,
  className,
  children,
  onEdit,
  showEditButton = false
}) => {
  // Map color scheme to Tailwind classes
  const colorClasses = {
    blue: 'border-blue-500 text-blue-500',
    green: 'border-green-500 text-green-500',
    purple: 'border-purple-500 text-purple-500',
    red: 'border-red-500 text-red-500',
    indigo: 'border-indigo-500 text-indigo-500',
  };

  const borderColorClass = `border-l-4 ${colorClasses[colorScheme].split(' ')[0]}`;
  const textColorClass = colorClasses[colorScheme].split(' ')[1];

  return (
    <div className={cn(
      "border rounded-md shadow-sm p-4",
      borderColorClass,
      className
    )}>
      <div className="flex flex-col">
        <div className="flex justify-between items-center mb-1">
          <h4 className="font-semibold">{title}</h4>
          {showEditButton && onEdit && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEdit();
              }}
              className="text-gray-400 hover:text-gray-600 p-1.5 rounded-full hover:bg-gray-100 transition-colors"
              title="Edit calculation and text"
            >
              <Edit size={16} />
            </button>
          )}
        </div>
        <p className="text-xs text-muted-foreground mb-2">{subtitle}</p>
        <div className="text-2xl font-bold mb-2">{formatCurrency(value)}</div>
        <div className={cn("flex items-center mt-auto", textColorClass)}>
          <Icon size={16} className="mr-1" />
          <span className="text-xs">{iconText}</span>
        </div>
        {children}
      </div>
    </div>
  );
};

export default IncomeCard;
