import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LucideIcon, Calculator, ArrowLeft } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface Calculation {
  label: string;
  value: string | number;
  isResult?: boolean;
  isSubCalculation?: boolean;
}

interface ShowWorkCardProps {
  title: string;
  subtitle: string;
  value: number;
  icon: LucideIcon;
  iconText: string;
  colorScheme: 'blue' | 'green' | 'purple' | 'red' | 'indigo';
  className?: string;
  calculations: Calculation[];
  onEdit?: () => void;
  showEditButton?: boolean;
}

const ShowWorkCard: React.FC<ShowWorkCardProps> = ({
  title,
  subtitle,
  value,
  icon: Icon,
  iconText,
  colorScheme,
  className,
  calculations,
  onEdit,
  showEditButton = false
}) => {
  const [showCalculations, setShowCalculations] = useState(false);

  // Map color scheme to Tailwind classes
  const colorClasses = {
    blue: 'border-blue-500 text-blue-500 bg-blue-50',
    green: 'border-green-500 text-green-500 bg-green-50',
    purple: 'border-purple-500 text-purple-500 bg-purple-50',
    red: 'border-red-500 text-red-500 bg-red-50',
    indigo: 'border-indigo-500 text-indigo-500 bg-indigo-50',
  };

  const borderColorClass = `border-l-4 ${colorClasses[colorScheme].split(' ')[0]}`;
  const textColorClass = colorClasses[colorScheme].split(' ')[1];
  const bgColorClass = colorClasses[colorScheme].split(' ')[2];

  const formatCalculationValue = (calcValue: string | number) => {
    if (typeof calcValue === 'number') {
      return formatCurrency(calcValue);
    }
    return calcValue;
  };

  return (
    <div className={cn(
      "border rounded-lg shadow-md overflow-hidden transition-all duration-300 bg-white",
      borderColorClass,
      showCalculations ? "min-h-[300px] shadow-xl" : "h-auto hover:shadow-lg hover:scale-[1.02] transform-gpu cursor-pointer",
      className
    )}>
      <AnimatePresence mode="wait">
        {!showCalculations ? (
          // Front of card - Summary view
          <motion.div
            key="front"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="p-5"
          >
            <div className="flex flex-col h-full">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-semibold text-gray-900 text-base">{title}</h4>
                {showEditButton && onEdit && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit();
                    }}
                    className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100 transition-all duration-200 hover:scale-110"
                    title="Edit calculation and text"
                  >
                    <Calculator size={16} />
                  </button>
                )}
              </div>
              <p className="text-sm text-gray-600 mb-3 leading-relaxed">{subtitle}</p>
              <div className="text-3xl font-bold mb-4 text-gray-900 tracking-tight">{formatCurrency(value)}</div>
              
              <div className="flex items-center justify-between mt-auto">
                <div className={cn("flex items-center", textColorClass)}>
                  <Icon size={16} className="mr-1" />
                  <span className="text-xs">{iconText}</span>
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCalculations(true)}
                  className={cn(
                    "text-xs h-8 px-3 transition-all duration-200 font-medium",
                    textColorClass,
                    "hover:bg-opacity-10 hover:scale-105 border-current"
                  )}
                >
                  <Calculator size={14} className="mr-1.5" />
                  Show Our Work
                </Button>
              </div>
            </div>
          </motion.div>
        ) : (
          // Back of card - Calculation details
          <motion.div
            key="back"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className={cn("p-5", bgColorClass)}
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-gray-900 text-lg">{title} Calculation</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCalculations(false)}
                className="text-gray-600 hover:text-gray-800 h-8 px-3 transition-all duration-200 hover:scale-105"
              >
                <ArrowLeft size={14} className="mr-1.5" />
                Back
              </Button>
            </div>
            
            <div className="space-y-2 text-sm">
              {calculations.map((calc, index) => (
                <div 
                  key={index} 
                  className={cn(
                    "flex justify-between items-center py-1",
                    calc.isSubCalculation && "pl-4 text-xs text-gray-600",
                    calc.isResult && "border-t border-gray-300 pt-2 mt-2 font-semibold text-base"
                  )}
                >
                  <span className="text-gray-700">{calc.label}:</span>
                  <span className={cn(
                    "font-medium text-right",
                    calc.isResult ? "text-gray-900 font-bold" : "text-gray-800"
                  )}>
                    {formatCalculationValue(calc.value)}
                  </span>
                </div>
              ))}
            </div>
            
            <div className="mt-4 pt-3 border-t border-gray-300">
              <div className="flex justify-between items-center">
                <span className="text-sm font-semibold text-gray-900">Final Result:</span>
                <span className="text-lg font-bold text-gray-900">{formatCurrency(value)}</span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ShowWorkCard;
