import React from 'react';
import ShowWorkCard from './ShowWorkCard';
import { Users } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface OverrideIncomeCardProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  overrideData?: {
    teamProduction?: number;
    teamMembers?: number;
    overrideRate?: number;
    commissionRate?: number;
    cdrRate?: number;
    teamStartYear?: number;
    projectionYears?: number;
    teamAUM?: number;
  };
  cardType?: 'team' | 'business' | 'expansion-office';
}

const OverrideIncomeCard: React.FC<OverrideIncomeCardProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  overrideData,
  cardType = 'team'
}) => {
  // Generate calculation steps based on card type
  const generateCalculations = () => {
    if (!overrideData) {
      return [
        { label: "No calculation data available", value: "N/A" }
      ];
    }

    const {
      teamProduction = 0,
      teamMembers = 3,
      overrideRate = 0.20, // 20% override rate
      commissionRate = 0.62,
      cdrRate = 0.055,
      teamStartYear = 3,
      projectionYears = 10,
      teamAUM = 0
    } = overrideData;

    // Calculate team production (starts after teamStartYear)
    const activeYears = Math.max(0, projectionYears - teamStartYear);
    const monthlyTeamProduction = teamProduction * teamMembers;
    const grossTeamCommission = monthlyTeamProduction * cdrRate;
    const teamCommission = grossTeamCommission * commissionRate;
    const productionOverride = teamCommission * overrideRate;

    // Calculate trail override if teamAUM is provided
    const trailRate = 0.0025; // 0.25% annual trail rate
    const trailOverrideRate = 0.19; // 19% trail override spread (57% - 38%)
    const annualTrailOverride = teamAUM > 0 ? teamAUM * trailRate * trailOverrideRate : 0;
    const monthlyTrailOverride = annualTrailOverride / 12;

    const totalOverrideCommission = productionOverride + monthlyTrailOverride;

    // Start with team start year and active years context
    const calculations = [];

    if (teamStartYear > 0) {
      calculations.push(
        { label: "Team Start Year", value: `Year ${teamStartYear + 1}` },
        { label: "Active Production Years", value: `${activeYears} years` }
      );
    }

    // Add production override calculations
    calculations.push(
      { label: "Team Members", value: `${teamMembers} ${cardType === 'expansion-office' ? 'expansion offices' : 'Licensed Reps'}` },
      { label: "Monthly Production per Member", value: formatCurrency(teamProduction) },
      { label: "Total Monthly Team Production", value: `${teamMembers} × ${formatCurrency(teamProduction)}`, isSubCalculation: true },
      { label: "Team Production Amount", value: formatCurrency(monthlyTeamProduction) },
      { label: "CDR Rate", value: `${(cdrRate * 100).toFixed(1)}%` },
      { label: "Gross Team Commission", value: `${formatCurrency(monthlyTeamProduction)} × ${(cdrRate * 100).toFixed(1)}%`, isSubCalculation: true },
      { label: "Gross Commission Amount", value: formatCurrency(grossTeamCommission) },
      { label: "Team Commission Rate", value: `${(commissionRate * 100).toFixed(1)}%` },
      { label: "Team Commission", value: `${formatCurrency(grossTeamCommission)} × ${(commissionRate * 100).toFixed(1)}%`, isSubCalculation: true },
      { label: "Team Commission Amount", value: formatCurrency(teamCommission) },
      { label: "Production Override Rate", value: `${(overrideRate * 100).toFixed(1)}%` },
      { label: "Production Override", value: `${formatCurrency(teamCommission)} × ${(overrideRate * 100).toFixed(1)}%`, isSubCalculation: true },
      { label: "Monthly Production Override", value: formatCurrency(productionOverride) }
    );

    // Add trail override calculations if teamAUM is provided
    if (teamAUM > 0) {
      calculations.push(
        { label: "Team AUM (After " + activeYears + " Years)", value: formatCurrency(teamAUM) },
        { label: "AUM Growth Note", value: "Includes continuous new investments + market growth", isSubCalculation: true },
        { label: "Annual Trail Rate", value: `${(trailRate * 100).toFixed(2)}%` },
        { label: "Trail Override Rate", value: `${(trailOverrideRate * 100).toFixed(1)}% (RVP 57% - RL 38%)` },
        { label: "Annual Trail Override", value: `${formatCurrency(teamAUM)} × ${(trailRate * 100).toFixed(2)}% × ${(trailOverrideRate * 100).toFixed(1)}%`, isSubCalculation: true },
        { label: "Annual Trail Override Amount", value: formatCurrency(annualTrailOverride) },
        { label: "Monthly Trail Override", value: `${formatCurrency(annualTrailOverride)} ÷ 12`, isSubCalculation: true },
        { label: "Monthly Trail Override Amount", value: formatCurrency(monthlyTrailOverride) }
      );
    }

    // Final calculation with clear breakdown
    if (teamAUM > 0) {
      calculations.push(
        { label: "Final Calculation", value: `${formatCurrency(productionOverride)} + ${formatCurrency(monthlyTrailOverride)}`, isSubCalculation: true },
        { label: "Total Monthly Override Income", value: formatCurrency(totalOverrideCommission), isResult: true }
      );
    } else {
      calculations.push(
        { label: "Total Monthly Override Income", value: formatCurrency(totalOverrideCommission), isResult: true }
      );
    }

    return calculations;
  };

  const getCardTitle = () => {
    switch (cardType) {
      case 'business': return 'Business Override Income';
      case 'expansion-office': return 'Expansion Office Override Income';
      default: return 'Team Override Income';
    }
  };

  const getCardSubtitle = () => {
    switch (cardType) {
      case 'business': return 'Override from team & expansion offices';
      case 'expansion-office': return 'Override from expansion office network';
      default: return 'Override from team production';
    }
  };

  return (
    <ShowWorkCard
      title={getCardTitle()}
      subtitle={getCardSubtitle()}
      value={value}
      icon={Users}
      iconText={cardType === 'expansion-office' ? 'Expansion Office Network' : 'Team Production'}
      colorScheme="indigo"
      className={className}
      calculations={generateCalculations()}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default OverrideIncomeCard;
