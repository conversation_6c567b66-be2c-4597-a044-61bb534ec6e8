import React from 'react';
import ShowWorkCard from './ShowWorkCard';
import { RefreshCw } from 'lucide-react';
import { formatCurrency } from '@/utils/formatting';

interface RecurringPacCardNewProps {
  value: number;
  className?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  pacData?: {
    pacPerCase: number;
    numPacCases: number;
    projectionYears: number;
    commissionRate?: number;
    cdrRate?: number;
  };
}

const RecurringPacCardNew: React.FC<RecurringPacCardNewProps> = ({
  value,
  className,
  onEdit,
  showEditButton,
  pacData
}) => {
  // Generate calculation steps
  const generateCalculations = () => {
    if (!pacData) {
      return [
        { label: "No calculation data available", value: "N/A" }
      ];
    }

    const { 
      pacPerCase, 
      numPacCases, 
      projectionYears,
      commissionRate = 0.425,
      cdrRate = 0.05 // 5% CDR for PAC
    } = pacData;

    const monthlyPacContribution = pacPerCase * numPacCases;
    const totalMonths = projectionYears * 12;
    const totalPacInvestment = monthlyPacContribution * totalMonths;
    const grossCommission = totalPacInvestment * cdrRate;
    const finalCommission = grossCommission * commissionRate;

    return [
      {
        label: "PAC per Case",
        value: formatCurrency(pacPerCase),
        isSubCalculation: false
      },
      {
        label: "Number of PAC Cases",
        value: `${numPacCases} cases`,
        isSubCalculation: false
      },
      {
        label: "Monthly PAC Total",
        value: `${numPacCases} × ${formatCurrency(pacPerCase)}`,
        isSubCalculation: true
      },
      {
        label: "Monthly PAC Amount",
        value: monthlyPacContribution,
        isSubCalculation: false
      },
      {
        label: "Projection Period",
        value: `${projectionYears} years (${totalMonths} months)`,
        isSubCalculation: false
      },
      {
        label: "Total PAC Investment",
        value: `${formatCurrency(monthlyPacContribution)} × ${totalMonths} months`,
        isSubCalculation: true
      },
      {
        label: "Total PAC Amount",
        value: totalPacInvestment,
        isSubCalculation: false
      },
      {
        label: "CDR Rate (PAC)",
        value: `${(cdrRate * 100).toFixed(1)}%`,
        isSubCalculation: false
      },
      {
        label: "Gross Commission",
        value: `${formatCurrency(totalPacInvestment)} × ${(cdrRate * 100).toFixed(1)}%`,
        isSubCalculation: true
      },
      {
        label: "Gross Commission Amount",
        value: grossCommission,
        isSubCalculation: false
      },
      {
        label: "Commission Rate",
        value: `${(commissionRate * 100).toFixed(1)}%`,
        isSubCalculation: false
      },
      {
        label: "Final Calculation",
        value: `${formatCurrency(grossCommission)} × ${(commissionRate * 100).toFixed(1)}%`,
        isSubCalculation: true
      },
      {
        label: "Recurring PAC Income",
        value: finalCommission,
        isResult: true
      }
    ];
  };

  return (
    <ShowWorkCard
      title="Recurring PAC Income"
      subtitle="Cumulative PAC contributions"
      value={value}
      icon={RefreshCw}
      iconText="Monthly PACs"
      colorScheme="green"
      className={className}
      calculations={generateCalculations()}
      onEdit={onEdit}
      showEditButton={showEditButton}
    />
  );
};

export default RecurringPacCardNew;
