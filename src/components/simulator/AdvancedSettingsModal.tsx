import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Settings, RotateCcw } from 'lucide-react';
import {
  ScenarioSettings,
  DEFAULT_SCENARIO_SETTINGS,
  FUND_TYPE_CONFIGS,
  REP_LEVEL_CONFIGS,
  SHARE_CLASS_CONFIGS
} from '@/types/scenarioSettings';

interface AdvancedSettingsModalProps {
  settings: ScenarioSettings;
  onSettingsChange: (settings: ScenarioSettings) => void;
}

const AdvancedSettingsModal: React.FC<AdvancedSettingsModalProps> = ({
  settings,
  onSettingsChange
}) => {
  const [localSettings, setLocalSettings] = useState<ScenarioSettings>(settings);
  const [isOpen, setIsOpen] = useState(false);

  const handleSave = () => {
    onSettingsChange(localSettings);
    setIsOpen(false);
  };

  const handleReset = () => {
    setLocalSettings(DEFAULT_SCENARIO_SETTINGS);
  };

  const updateSettings = (path: string, value: any) => {
    const keys = path.split('.');
    const newSettings = { ...localSettings };
    let current: any = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    setLocalSettings(newSettings);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className="h-8 w-8">
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Settings className="h-5 w-5" />
            Advanced Scenario Settings
          </DialogTitle>
          <DialogDescription className="text-base">
            Configure investment vehicles, rep levels, and scenario defaults that control all calculations.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Tabs defaultValue="display" className="w-full">
            <TabsList className="grid w-full grid-cols-5 mb-6">
              <TabsTrigger value="display">Display</TabsTrigger>
              <TabsTrigger value="investment">Investment</TabsTrigger>
              <TabsTrigger value="replevels">Rep Levels</TabsTrigger>
              <TabsTrigger value="production">Production</TabsTrigger>
              <TabsTrigger value="defaults">Defaults</TabsTrigger>
            </TabsList>

          {/* Display Mode Settings */}
          <TabsContent value="display" className="space-y-6">
            <div className="space-y-6">
              <div className="space-y-3">
                <Label htmlFor="displayMode" className="text-base font-medium">Display Mode</Label>
                <Select
                  value={localSettings.displayMode}
                  onValueChange={(value) => updateSettings('displayMode', value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public Mode</SelectItem>
                    <SelectItem value="company">Company Mode</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="p-6 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-semibold text-blue-900 mb-4 text-lg">Mode Descriptions</h4>
                <div className="space-y-4 text-blue-700">
                  <div className="space-y-2">
                    <div className="font-medium text-blue-800">Public Mode:</div>
                    <div className="text-sm leading-relaxed">
                      Uses generic terminology like "Self Employed", "Business Owner" - ideal for prospect presentations and new recruit conversations.
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="font-medium text-blue-800">Company Mode:</div>
                    <div className="text-sm leading-relaxed">
                      Uses Primerica terminology like "REP", "RVP", "1st Generation Override" - ideal for internal training and detailed planning.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Investment Vehicle Settings */}
          <TabsContent value="investment" className="space-y-6">
            {/* Investment Type Mix */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Investment Type Allocation (%)</h3>
              <div className="grid grid-cols-3 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="equityMix" className="text-base font-medium">Equity Funds</Label>
                  <Input
                    id="equityMix"
                    type="number"
                    min="0"
                    max="100"
                    className="text-center text-lg"
                    value={localSettings.investmentVehicle.investmentMix.equity}
                    onChange={(e) => updateSettings('investmentVehicle.investmentMix.equity', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="fixedIncomeMix" className="text-base font-medium">Fixed Income</Label>
                  <Input
                    id="fixedIncomeMix"
                    type="number"
                    min="0"
                    max="100"
                    className="text-center text-lg"
                    value={localSettings.investmentVehicle.investmentMix.fixedIncome}
                    onChange={(e) => updateSettings('investmentVehicle.investmentMix.fixedIncome', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="municipalBondsMix" className="text-base font-medium">Municipal Bonds</Label>
                  <Input
                    id="municipalBondsMix"
                    type="number"
                    min="0"
                    max="100"
                    className="text-center text-lg"
                    value={localSettings.investmentVehicle.investmentMix.municipalBonds}
                    onChange={(e) => updateSettings('investmentVehicle.investmentMix.municipalBonds', parseInt(e.target.value))}
                  />
                </div>
              </div>
              <div className="text-base font-medium text-center py-2">
                Total: {localSettings.investmentVehicle.investmentMix.equity +
                       localSettings.investmentVehicle.investmentMix.fixedIncome +
                       localSettings.investmentVehicle.investmentMix.municipalBonds}%
                {(localSettings.investmentVehicle.investmentMix.equity +
                  localSettings.investmentVehicle.investmentMix.fixedIncome +
                  localSettings.investmentVehicle.investmentMix.municipalBonds) !== 100 &&
                  <span className="text-red-600 ml-2 font-semibold">(Must total 100%)</span>}
              </div>
            </div>

            {/* Share Class Mix */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Share Class Distribution (%)</h3>
              <div className="grid grid-cols-3 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="classAMix" className="text-base font-medium">Class A Shares</Label>
                  <Input
                    id="classAMix"
                    type="number"
                    min="0"
                    max="100"
                    className="text-center text-lg"
                    value={localSettings.investmentVehicle.shareClassMix.classA}
                    onChange={(e) => updateSettings('investmentVehicle.shareClassMix.classA', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="classBMix" className="text-base font-medium">Class B Shares</Label>
                  <Input
                    id="classBMix"
                    type="number"
                    min="0"
                    max="100"
                    className="text-center text-lg"
                    value={localSettings.investmentVehicle.shareClassMix.classB}
                    onChange={(e) => updateSettings('investmentVehicle.shareClassMix.classB', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="classCMix" className="text-base font-medium">Class C Shares</Label>
                  <Input
                    id="classCMix"
                    type="number"
                    min="0"
                    max="100"
                    className="text-center text-lg"
                    value={localSettings.investmentVehicle.shareClassMix.classC}
                    onChange={(e) => updateSettings('investmentVehicle.shareClassMix.classC', parseInt(e.target.value))}
                  />
                </div>
              </div>
              <div className="text-base font-medium text-center py-2">
                Total: {localSettings.investmentVehicle.shareClassMix.classA +
                       localSettings.investmentVehicle.shareClassMix.classB +
                       localSettings.investmentVehicle.shareClassMix.classC}%
                {(localSettings.investmentVehicle.shareClassMix.classA +
                  localSettings.investmentVehicle.shareClassMix.classB +
                  localSettings.investmentVehicle.shareClassMix.classC) !== 100 &&
                  <span className="text-red-600 ml-2 font-semibold">(Must total 100%)</span>}
              </div>
            </div>

            {/* Trail Rate */}
            <div className="space-y-3">
              <Label htmlFor="trailRate" className="text-base font-medium">Annual Trail Rate (%)</Label>
              <div className="w-48">
                <Input
                  id="trailRate"
                  type="number"
                  step="0.01"
                  min="0"
                  max="5"
                  className="text-center text-lg"
                  value={(localSettings.investmentVehicle.trailRate * 100).toFixed(2)}
                  onChange={(e) => updateSettings('investmentVehicle.trailRate', parseFloat(e.target.value) / 100)}
                />
              </div>
            </div>
          </TabsContent>

          {/* Rep Level Settings */}
          <TabsContent value="replevels" className="space-y-6">
            {/* Self Employed Card Level */}
            <div className="space-y-3">
              <Label htmlFor="selfEmployedLevel" className="text-base font-medium">Self Employed Card Rep Level</Label>
              <Select
                value={localSettings.repLevels.selfEmployedLevel}
                onValueChange={(value) => updateSettings('repLevels.selfEmployedLevel', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(REP_LEVEL_CONFIGS).filter(([key]) => key !== 'RVP').map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      {config.name} ({config.code}) - {(config.directCommission * 100).toFixed(1)}%
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-600">
                Rep level for the "Self Employed" scenario card (REP through PRL)
              </p>
            </div>

            {/* Base Shop Composition */}
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Business Owner Base Shop Composition</h3>
                <p className="text-sm text-gray-600">Configure the number of each rep level in the RVP's base shop</p>
              </div>

              <div className="grid grid-cols-4 gap-4">
                {Object.entries(REP_LEVEL_CONFIGS).filter(([key]) => key !== 'RVP').map(([key, config]) => (
                  <div key={key} className="space-y-3">
                    <Label htmlFor={`baseShop${key}`} className="text-sm font-medium">{config.name}</Label>
                    <Input
                      id={`baseShop${key}`}
                      type="number"
                      min="0"
                      max="50"
                      className="text-center"
                      value={localSettings.repLevels.baseShopComposition[key as keyof typeof localSettings.repLevels.baseShopComposition]}
                      onChange={(e) => updateSettings(`repLevels.baseShopComposition.${key}`, parseInt(e.target.value))}
                    />
                  </div>
                ))}
              </div>

              <div className="text-base font-medium text-center py-2">
                Total Base Shop Size: {Object.values(localSettings.repLevels.baseShopComposition).reduce((sum, count) => sum + count, 0)} reps
              </div>
            </div>

            {/* Commission Rate Display */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Current Commission Rates</h3>
              <div className="grid grid-cols-2 gap-6">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-900 mb-3">
                    Self Employed Level: {REP_LEVEL_CONFIGS[localSettings.repLevels.selfEmployedLevel].name}
                  </h4>
                  <div className="text-sm text-blue-700 space-y-2">
                    <div className="flex justify-between">
                      <span>Direct:</span>
                      <span className="font-medium">{(REP_LEVEL_CONFIGS[localSettings.repLevels.selfEmployedLevel].directCommission * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>PAC:</span>
                      <span className="font-medium">{(REP_LEVEL_CONFIGS[localSettings.repLevels.selfEmployedLevel].pacCommission * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Trail:</span>
                      <span className="font-medium">{(REP_LEVEL_CONFIGS[localSettings.repLevels.selfEmployedLevel].trailCommission * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h4 className="font-semibold text-green-900 mb-3">
                    Business Owner Level: {REP_LEVEL_CONFIGS.RVP.name}
                  </h4>
                  <div className="text-sm text-green-700 space-y-2">
                    <div className="flex justify-between">
                      <span>Direct:</span>
                      <span className="font-medium">{(REP_LEVEL_CONFIGS.RVP.directCommission * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>PAC:</span>
                      <span className="font-medium">{(REP_LEVEL_CONFIGS.RVP.pacCommission * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Trail:</span>
                      <span className="font-medium">{(REP_LEVEL_CONFIGS.RVP.trailCommission * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Production Settings */}
          <TabsContent value="production" className="space-y-6">
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Production & Override Settings</h3>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="teamProductionRatio" className="text-base font-medium">Team Production Ratio (%)</Label>
                  <Input
                    id="teamProductionRatio"
                    type="number"
                    step="5"
                    min="0"
                    max="100"
                    className="text-center text-lg"
                    value={(localSettings.productionSettings.teamProductionRatio * 100).toFixed(0)}
                    onChange={(e) => updateSettings('productionSettings.teamProductionRatio', parseFloat(e.target.value) / 100)}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="agencyStartYear" className="text-base font-medium">Expansion Office Start Year</Label>
                  <Input
                    id="agencyStartYear"
                    type="number"
                    min="1"
                    max="20"
                    className="text-center text-lg"
                    value={localSettings.productionSettings.agencyStartYear}
                    onChange={(e) => updateSettings('productionSettings.agencyStartYear', parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="teamOverride" className="text-base font-medium">Team Override Rate (%)</Label>
                  <Input
                    id="teamOverride"
                    type="number"
                    step="0.5"
                    min="0"
                    max="50"
                    className="text-center text-lg"
                    value={(localSettings.productionSettings.overrideRates.teamOverride * 100).toFixed(1)}
                    onChange={(e) => updateSettings('productionSettings.overrideRates.teamOverride', parseFloat(e.target.value) / 100)}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="agencyOverride" className="text-base font-medium">Expansion Office Override Rate (%)</Label>
                  <Input
                    id="agencyOverride"
                    type="number"
                    step="0.5"
                    min="0"
                    max="50"
                    className="text-center text-lg"
                    value={(localSettings.productionSettings.overrideRates.agencyOverride * 100).toFixed(1)}
                    onChange={(e) => updateSettings('productionSettings.overrideRates.agencyOverride', parseFloat(e.target.value) / 100)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Scenario Defaults */}
          <TabsContent value="defaults" className="space-y-6">
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Scenario Defaults</h3>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="projectionYears" className="text-base font-medium">Default Projection Years</Label>
                  <Input
                    id="projectionYears"
                    type="number"
                    min="1"
                    max="50"
                    className="text-center text-lg"
                    value={localSettings.scenarioDefaults.projectionYears}
                    onChange={(e) => updateSettings('scenarioDefaults.projectionYears', parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="marketGrowthRate" className="text-base font-medium">Market Growth Rate (%)</Label>
                  <Input
                    id="marketGrowthRate"
                    type="number"
                    step="0.5"
                    min="-10"
                    max="30"
                    className="text-center text-lg"
                    value={(localSettings.scenarioDefaults.marketGrowthRate * 100).toFixed(1)}
                    onChange={(e) => updateSettings('scenarioDefaults.marketGrowthRate', parseFloat(e.target.value) / 100)}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="defaultAgencies" className="text-base font-medium">Default Expansion Offices</Label>
                  <Input
                    id="defaultAgencies"
                    type="number"
                    min="1"
                    max="20"
                    className="text-center text-lg"
                    value={localSettings.scenarioDefaults.defaultAgencies}
                    onChange={(e) => updateSettings('scenarioDefaults.defaultAgencies', parseInt(e.target.value))}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="defaultRLsPerAgency" className="text-base font-medium">Default Licensed Reps per Office</Label>
                  <Input
                    id="defaultRLsPerAgency"
                    type="number"
                    min="1"
                    max="20"
                    className="text-center text-lg"
                    value={localSettings.scenarioDefaults.defaultRLsPerAgency}
                    onChange={(e) => updateSettings('scenarioDefaults.defaultRLsPerAgency', parseInt(e.target.value))}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-between pt-6 border-t mt-8">
          <Button variant="outline" onClick={handleReset} className="flex items-center gap-2">
            <RotateCcw className="h-4 w-4" />
            Reset to Defaults
          </Button>
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
              Save Settings
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedSettingsModal;
