import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Settings, RotateCcw } from 'lucide-react';
import {
  ScenarioSettings,
  DEFAULT_SCENARIO_SETTINGS,
  FUND_TYPE_CONFIGS,
  REP_LEVEL_CONFIGS,
  SHARE_CLASS_CONFIGS
} from '@/types/scenarioSettings';

interface AdvancedSettingsModalProps {
  settings: ScenarioSettings;
  onSettingsChange: (settings: ScenarioSettings) => void;
}

const AdvancedSettingsModal: React.FC<AdvancedSettingsModalProps> = ({
  settings,
  onSettingsChange
}) => {
  const [localSettings, setLocalSettings] = useState<ScenarioSettings>(settings);
  const [isOpen, setIsOpen] = useState(false);

  const handleSave = () => {
    onSettingsChange(localSettings);
    setIsOpen(false);
  };

  const handleReset = () => {
    setLocalSettings(DEFAULT_SCENARIO_SETTINGS);
  };

  const updateSettings = (path: string, value: any) => {
    const keys = path.split('.');
    const newSettings = { ...localSettings };
    let current: any = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    setLocalSettings(newSettings);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className="h-8 w-8">
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Advanced Scenario Settings
          </DialogTitle>
          <DialogDescription>
            Configure investment vehicles, rep levels, and scenario defaults that control all calculations.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="display" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="display">Display</TabsTrigger>
            <TabsTrigger value="investment">Investment</TabsTrigger>
            <TabsTrigger value="replevels">Rep Levels</TabsTrigger>
            <TabsTrigger value="production">Production</TabsTrigger>
            <TabsTrigger value="defaults">Defaults</TabsTrigger>
          </TabsList>

          {/* Display Mode Settings */}
          <TabsContent value="display" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Display Mode Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="displayMode">Display Mode</Label>
                    <Select
                      value={localSettings.displayMode}
                      onValueChange={(value) => updateSettings('displayMode', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="public">Public Mode</SelectItem>
                        <SelectItem value="company">Company Mode</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Mode Descriptions</h4>
                    <div className="space-y-2 text-sm text-blue-700">
                      <div>
                        <strong>Public Mode:</strong> Uses generic terminology like "Self Employed", "Business Owner" - ideal for prospect presentations and new recruit conversations.
                      </div>
                      <div>
                        <strong>Company Mode:</strong> Uses Primerica terminology like "REP", "RVP", "1st Generation Override" - ideal for internal training and detailed planning.
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Investment Vehicle Settings */}
          <TabsContent value="investment" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Investment Mix Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Investment Type Mix */}
                <div className="space-y-4">
                  <h4 className="font-medium">Investment Type Allocation (%)</h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="equityMix">Equity Funds</Label>
                      <Input
                        id="equityMix"
                        type="number"
                        min="0"
                        max="100"
                        value={localSettings.investmentVehicle.investmentMix.equity}
                        onChange={(e) => updateSettings('investmentVehicle.investmentMix.equity', parseInt(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="fixedIncomeMix">Fixed Income</Label>
                      <Input
                        id="fixedIncomeMix"
                        type="number"
                        min="0"
                        max="100"
                        value={localSettings.investmentVehicle.investmentMix.fixedIncome}
                        onChange={(e) => updateSettings('investmentVehicle.investmentMix.fixedIncome', parseInt(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="municipalBondsMix">Municipal Bonds</Label>
                      <Input
                        id="municipalBondsMix"
                        type="number"
                        min="0"
                        max="100"
                        value={localSettings.investmentVehicle.investmentMix.municipalBonds}
                        onChange={(e) => updateSettings('investmentVehicle.investmentMix.municipalBonds', parseInt(e.target.value))}
                      />
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    Total: {localSettings.investmentVehicle.investmentMix.equity +
                           localSettings.investmentVehicle.investmentMix.fixedIncome +
                           localSettings.investmentVehicle.investmentMix.municipalBonds}%
                    {(localSettings.investmentVehicle.investmentMix.equity +
                      localSettings.investmentVehicle.investmentMix.fixedIncome +
                      localSettings.investmentVehicle.investmentMix.municipalBonds) !== 100 &&
                      <span className="text-red-600 ml-2">(Must total 100%)</span>}
                  </div>
                </div>

                {/* Share Class Mix */}
                <div className="space-y-4">
                  <h4 className="font-medium">Share Class Distribution (%)</h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="classAMix">Class A Shares</Label>
                      <Input
                        id="classAMix"
                        type="number"
                        min="0"
                        max="100"
                        value={localSettings.investmentVehicle.shareClassMix.classA}
                        onChange={(e) => updateSettings('investmentVehicle.shareClassMix.classA', parseInt(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="classBMix">Class B Shares</Label>
                      <Input
                        id="classBMix"
                        type="number"
                        min="0"
                        max="100"
                        value={localSettings.investmentVehicle.shareClassMix.classB}
                        onChange={(e) => updateSettings('investmentVehicle.shareClassMix.classB', parseInt(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="classCMix">Class C Shares</Label>
                      <Input
                        id="classCMix"
                        type="number"
                        min="0"
                        max="100"
                        value={localSettings.investmentVehicle.shareClassMix.classC}
                        onChange={(e) => updateSettings('investmentVehicle.shareClassMix.classC', parseInt(e.target.value))}
                      />
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    Total: {localSettings.investmentVehicle.shareClassMix.classA +
                           localSettings.investmentVehicle.shareClassMix.classB +
                           localSettings.investmentVehicle.shareClassMix.classC}%
                    {(localSettings.investmentVehicle.shareClassMix.classA +
                      localSettings.investmentVehicle.shareClassMix.classB +
                      localSettings.investmentVehicle.shareClassMix.classC) !== 100 &&
                      <span className="text-red-600 ml-2">(Must total 100%)</span>}
                  </div>
                </div>

                {/* Trail Rate */}
                <div className="space-y-2">
                  <Label htmlFor="trailRate">Annual Trail Rate (%)</Label>
                  <Input
                    id="trailRate"
                    type="number"
                    step="0.01"
                    min="0"
                    max="5"
                    value={(localSettings.investmentVehicle.trailRate * 100).toFixed(2)}
                    onChange={(e) => updateSettings('investmentVehicle.trailRate', parseFloat(e.target.value) / 100)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rep Level Settings */}
          <TabsContent value="replevels" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Rep Level Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Self Employed Card Level */}
                <div className="space-y-2">
                  <Label htmlFor="selfEmployedLevel">Self Employed Card Rep Level</Label>
                  <Select
                    value={localSettings.repLevels.selfEmployedLevel}
                    onValueChange={(value) => updateSettings('repLevels.selfEmployedLevel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(REP_LEVEL_CONFIGS).filter(([key]) => key !== 'RVP').map(([key, config]) => (
                        <SelectItem key={key} value={key}>
                          {config.name} ({config.code}) - {(config.directCommission * 100).toFixed(1)}%
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-600">
                    Rep level for the "Self Employed" scenario card (REP through PRL)
                  </p>
                </div>

                {/* Base Shop Composition */}
                <div className="space-y-4">
                  <h4 className="font-medium">Business Owner Base Shop Composition</h4>
                  <p className="text-sm text-gray-600">Configure the number of each rep level in the RVP's base shop</p>

                  <div className="grid grid-cols-4 gap-4">
                    {Object.entries(REP_LEVEL_CONFIGS).filter(([key]) => key !== 'RVP').map(([key, config]) => (
                      <div key={key} className="space-y-2">
                        <Label htmlFor={`baseShop${key}`}>{config.name}</Label>
                        <Input
                          id={`baseShop${key}`}
                          type="number"
                          min="0"
                          max="50"
                          value={localSettings.repLevels.baseShopComposition[key as keyof typeof localSettings.repLevels.baseShopComposition]}
                          onChange={(e) => updateSettings(`repLevels.baseShopComposition.${key}`, parseInt(e.target.value))}
                        />
                      </div>
                    ))}
                  </div>

                  <div className="text-sm text-gray-600">
                    Total Base Shop Size: {Object.values(localSettings.repLevels.baseShopComposition).reduce((sum, count) => sum + count, 0)} reps
                  </div>
                </div>

                {/* Commission Rate Display */}
                <div className="space-y-4">
                  <h4 className="font-medium">Current Commission Rates</h4>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <h5 className="font-medium text-blue-900 mb-2">
                      Self Employed Level: {REP_LEVEL_CONFIGS[localSettings.repLevels.selfEmployedLevel].name}
                    </h5>
                    <div className="text-sm text-blue-700 space-y-1">
                      <div>Direct: {(REP_LEVEL_CONFIGS[localSettings.repLevels.selfEmployedLevel].directCommission * 100).toFixed(1)}%</div>
                      <div>PAC: {(REP_LEVEL_CONFIGS[localSettings.repLevels.selfEmployedLevel].pacCommission * 100).toFixed(1)}%</div>
                      <div>Trail: {(REP_LEVEL_CONFIGS[localSettings.repLevels.selfEmployedLevel].trailCommission * 100).toFixed(1)}%</div>
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 rounded-lg">
                    <h5 className="font-medium text-green-900 mb-2">
                      Business Owner Level: {REP_LEVEL_CONFIGS.RVP.name}
                    </h5>
                    <div className="text-sm text-green-700 space-y-1">
                      <div>Direct: {(REP_LEVEL_CONFIGS.RVP.directCommission * 100).toFixed(1)}%</div>
                      <div>PAC: {(REP_LEVEL_CONFIGS.RVP.pacCommission * 100).toFixed(1)}%</div>
                      <div>Trail: {(REP_LEVEL_CONFIGS.RVP.trailCommission * 100).toFixed(1)}%</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Production Settings */}
          <TabsContent value="production" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Production & Override Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="teamProductionRatio">Team Production Ratio (%)</Label>
                    <Input
                      id="teamProductionRatio"
                      type="number"
                      step="5"
                      min="0"
                      max="100"
                      value={(localSettings.productionSettings.teamProductionRatio * 100).toFixed(0)}
                      onChange={(e) => updateSettings('productionSettings.teamProductionRatio', parseFloat(e.target.value) / 100)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="agencyStartYear">Agency Start Year</Label>
                    <Input
                      id="agencyStartYear"
                      type="number"
                      min="1"
                      max="20"
                      value={localSettings.productionSettings.agencyStartYear}
                      onChange={(e) => updateSettings('productionSettings.agencyStartYear', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="teamOverride">Team Override Rate (%)</Label>
                    <Input
                      id="teamOverride"
                      type="number"
                      step="0.5"
                      min="0"
                      max="50"
                      value={(localSettings.productionSettings.overrideRates.teamOverride * 100).toFixed(1)}
                      onChange={(e) => updateSettings('productionSettings.overrideRates.teamOverride', parseFloat(e.target.value) / 100)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="agencyOverride">Agency Override Rate (%)</Label>
                    <Input
                      id="agencyOverride"
                      type="number"
                      step="0.5"
                      min="0"
                      max="50"
                      value={(localSettings.productionSettings.overrideRates.agencyOverride * 100).toFixed(1)}
                      onChange={(e) => updateSettings('productionSettings.overrideRates.agencyOverride', parseFloat(e.target.value) / 100)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Scenario Defaults */}
          <TabsContent value="defaults" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Scenario Defaults</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="projectionYears">Default Projection Years</Label>
                    <Input
                      id="projectionYears"
                      type="number"
                      min="1"
                      max="50"
                      value={localSettings.scenarioDefaults.projectionYears}
                      onChange={(e) => updateSettings('scenarioDefaults.projectionYears', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="marketGrowthRate">Market Growth Rate (%)</Label>
                    <Input
                      id="marketGrowthRate"
                      type="number"
                      step="0.5"
                      min="-10"
                      max="30"
                      value={(localSettings.scenarioDefaults.marketGrowthRate * 100).toFixed(1)}
                      onChange={(e) => updateSettings('scenarioDefaults.marketGrowthRate', parseFloat(e.target.value) / 100)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="defaultAgencies">Default Agencies</Label>
                    <Input
                      id="defaultAgencies"
                      type="number"
                      min="1"
                      max="20"
                      value={localSettings.scenarioDefaults.defaultAgencies}
                      onChange={(e) => updateSettings('scenarioDefaults.defaultAgencies', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="defaultRLsPerAgency">Default RLs per Agency</Label>
                    <Input
                      id="defaultRLsPerAgency"
                      type="number"
                      min="1"
                      max="20"
                      value={localSettings.scenarioDefaults.defaultRLsPerAgency}
                      onChange={(e) => updateSettings('scenarioDefaults.defaultRLsPerAgency', parseInt(e.target.value))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={handleReset} className="flex items-center gap-2">
            <RotateCcw className="h-4 w-4" />
            Reset to Defaults
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedSettingsModal;
