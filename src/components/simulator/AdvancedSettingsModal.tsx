import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Settings, RotateCcw } from 'lucide-react';
import { 
  ScenarioSettings, 
  DEFAULT_SCENARIO_SETTINGS, 
  FUND_TYPE_CONFIGS, 
  REP_LEVEL_CONFIGS 
} from '@/types/scenarioSettings';

interface AdvancedSettingsModalProps {
  settings: ScenarioSettings;
  onSettingsChange: (settings: ScenarioSettings) => void;
}

const AdvancedSettingsModal: React.FC<AdvancedSettingsModalProps> = ({
  settings,
  onSettingsChange
}) => {
  const [localSettings, setLocalSettings] = useState<ScenarioSettings>(settings);
  const [isOpen, setIsOpen] = useState(false);

  const handleSave = () => {
    onSettingsChange(localSettings);
    setIsOpen(false);
  };

  const handleReset = () => {
    setLocalSettings(DEFAULT_SCENARIO_SETTINGS);
  };

  const updateSettings = (path: string, value: any) => {
    const keys = path.split('.');
    const newSettings = { ...localSettings };
    let current: any = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    setLocalSettings(newSettings);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className="h-8 w-8">
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Advanced Scenario Settings
          </DialogTitle>
          <DialogDescription>
            Configure investment vehicles, rep levels, and scenario defaults that control all calculations.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="investment" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="investment">Investment</TabsTrigger>
            <TabsTrigger value="replevels">Rep Levels</TabsTrigger>
            <TabsTrigger value="production">Production</TabsTrigger>
            <TabsTrigger value="defaults">Defaults</TabsTrigger>
          </TabsList>

          {/* Investment Vehicle Settings */}
          <TabsContent value="investment" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Investment Vehicle Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fundType">Fund Type</Label>
                    <Select
                      value={localSettings.investmentVehicle.fundType}
                      onValueChange={(value) => updateSettings('investmentVehicle.fundType', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(FUND_TYPE_CONFIGS).map(([key, config]) => (
                          <SelectItem key={key} value={key}>
                            {config.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-gray-600">
                      {FUND_TYPE_CONFIGS[localSettings.investmentVehicle.fundType].description}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="trailRate">Annual Trail Rate (%)</Label>
                    <Input
                      id="trailRate"
                      type="number"
                      step="0.01"
                      min="0"
                      max="5"
                      value={(localSettings.investmentVehicle.trailRate * 100).toFixed(2)}
                      onChange={(e) => updateSettings('investmentVehicle.trailRate', parseFloat(e.target.value) / 100)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rep Level Settings */}
          <TabsContent value="replevels" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Rep Level Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryRepLevel">Primary Rep Level</Label>
                    <Select
                      value={localSettings.repLevels.primaryRepLevel}
                      onValueChange={(value) => updateSettings('repLevels.primaryRepLevel', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(REP_LEVEL_CONFIGS).map(([key, config]) => (
                          <SelectItem key={key} value={key}>
                            {config.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="downlineRepLevel">Downline Rep Level</Label>
                    <Select
                      value={localSettings.repLevels.downlineRepLevel}
                      onValueChange={(value) => updateSettings('repLevels.downlineRepLevel', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(REP_LEVEL_CONFIGS).map(([key, config]) => (
                          <SelectItem key={key} value={key}>
                            {config.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Commission Rate Display */}
                <div className="mt-6">
                  <h4 className="font-medium mb-3">Current Commission Rates</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <h5 className="font-medium text-blue-900">
                        Primary ({REP_LEVEL_CONFIGS[localSettings.repLevels.primaryRepLevel].name})
                      </h5>
                      <div className="text-sm text-blue-700 space-y-1">
                        <div>Direct: {(REP_LEVEL_CONFIGS[localSettings.repLevels.primaryRepLevel].directCommission * 100).toFixed(1)}%</div>
                        <div>PAC: {(REP_LEVEL_CONFIGS[localSettings.repLevels.primaryRepLevel].pacCommission * 100).toFixed(1)}%</div>
                        <div>Trail: {(REP_LEVEL_CONFIGS[localSettings.repLevels.primaryRepLevel].trailCommission * 100).toFixed(1)}%</div>
                      </div>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <h5 className="font-medium text-green-900">
                        Downline ({REP_LEVEL_CONFIGS[localSettings.repLevels.downlineRepLevel].name})
                      </h5>
                      <div className="text-sm text-green-700 space-y-1">
                        <div>Direct: {(REP_LEVEL_CONFIGS[localSettings.repLevels.downlineRepLevel].directCommission * 100).toFixed(1)}%</div>
                        <div>PAC: {(REP_LEVEL_CONFIGS[localSettings.repLevels.downlineRepLevel].pacCommission * 100).toFixed(1)}%</div>
                        <div>Trail: {(REP_LEVEL_CONFIGS[localSettings.repLevels.downlineRepLevel].trailCommission * 100).toFixed(1)}%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Production Settings */}
          <TabsContent value="production" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Production & Override Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="teamProductionRatio">Team Production Ratio (%)</Label>
                    <Input
                      id="teamProductionRatio"
                      type="number"
                      step="5"
                      min="0"
                      max="100"
                      value={(localSettings.productionSettings.teamProductionRatio * 100).toFixed(0)}
                      onChange={(e) => updateSettings('productionSettings.teamProductionRatio', parseFloat(e.target.value) / 100)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="agencyStartYear">Agency Start Year</Label>
                    <Input
                      id="agencyStartYear"
                      type="number"
                      min="1"
                      max="20"
                      value={localSettings.productionSettings.agencyStartYear}
                      onChange={(e) => updateSettings('productionSettings.agencyStartYear', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="teamOverride">Team Override Rate (%)</Label>
                    <Input
                      id="teamOverride"
                      type="number"
                      step="0.5"
                      min="0"
                      max="50"
                      value={(localSettings.productionSettings.overrideRates.teamOverride * 100).toFixed(1)}
                      onChange={(e) => updateSettings('productionSettings.overrideRates.teamOverride', parseFloat(e.target.value) / 100)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="agencyOverride">Agency Override Rate (%)</Label>
                    <Input
                      id="agencyOverride"
                      type="number"
                      step="0.5"
                      min="0"
                      max="50"
                      value={(localSettings.productionSettings.overrideRates.agencyOverride * 100).toFixed(1)}
                      onChange={(e) => updateSettings('productionSettings.overrideRates.agencyOverride', parseFloat(e.target.value) / 100)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Scenario Defaults */}
          <TabsContent value="defaults" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Scenario Defaults</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="projectionYears">Default Projection Years</Label>
                    <Input
                      id="projectionYears"
                      type="number"
                      min="1"
                      max="50"
                      value={localSettings.scenarioDefaults.projectionYears}
                      onChange={(e) => updateSettings('scenarioDefaults.projectionYears', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="marketGrowthRate">Market Growth Rate (%)</Label>
                    <Input
                      id="marketGrowthRate"
                      type="number"
                      step="0.5"
                      min="-10"
                      max="30"
                      value={(localSettings.scenarioDefaults.marketGrowthRate * 100).toFixed(1)}
                      onChange={(e) => updateSettings('scenarioDefaults.marketGrowthRate', parseFloat(e.target.value) / 100)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="defaultAgencies">Default Agencies</Label>
                    <Input
                      id="defaultAgencies"
                      type="number"
                      min="1"
                      max="20"
                      value={localSettings.scenarioDefaults.defaultAgencies}
                      onChange={(e) => updateSettings('scenarioDefaults.defaultAgencies', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="defaultRLsPerAgency">Default RLs per Agency</Label>
                    <Input
                      id="defaultRLsPerAgency"
                      type="number"
                      min="1"
                      max="20"
                      value={localSettings.scenarioDefaults.defaultRLsPerAgency}
                      onChange={(e) => updateSettings('scenarioDefaults.defaultRLsPerAgency', parseInt(e.target.value))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={handleReset} className="flex items-center gap-2">
            <RotateCcw className="h-4 w-4" />
            Reset to Defaults
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedSettingsModal;
