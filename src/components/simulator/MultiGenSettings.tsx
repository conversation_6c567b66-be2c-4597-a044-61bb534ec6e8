
import React from 'react';
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { formatCurrency } from '@/utils/formatting';
import { overrideGenerations } from '@/simConfig/repInfo';

interface MultiGenSettingsProps {
  enableMultiGen: boolean;
  setEnableMultiGen: (enabled: boolean) => void;
  genOneAgents: number;
  setGenOneAgents: (value: number) => void;
  genTwoAgents: number;
  setGenTwoAgents: (value: number) => void;
  genThreeAgents: number;
  setGenThreeAgents: (value: number) => void;
  genOneRolloverCases: number;
  setGenOneRolloverCases: (value: number) => void;
  genTwoRolloverCases: number;
  setGenTwoRolloverCases: (value: number) => void;
  genThreeRolloverCases: number;
  setGenThreeRolloverCases: (value: number) => void;
  genOneMonthlyCases: number;
  setGenOneMonthlyCases: (value: number) => void;
  genTwoMonthlyCases: number;
  setGenTwoMonthlyCases: (value: number) => void;
  genThreeMonthlyCases: number;
  setGenThreeMonthlyCases: (value: number) => void;
  avgInitialInvestment: number;
  avgMonthlyContribution: number;
}

const MultiGenSettings: React.FC<MultiGenSettingsProps> = ({
  enableMultiGen,
  setEnableMultiGen,
  genOneAgents,
  setGenOneAgents,
  genTwoAgents,
  setGenTwoAgents,
  genThreeAgents,
  setGenThreeAgents,
  genOneRolloverCases,
  setGenOneRolloverCases,
  genTwoRolloverCases,
  setGenTwoRolloverCases,
  genThreeRolloverCases,
  setGenThreeRolloverCases,
  genOneMonthlyCases,
  setGenOneMonthlyCases,
  genTwoMonthlyCases,
  setGenTwoMonthlyCases,
  genThreeMonthlyCases,
  setGenThreeMonthlyCases,
  avgInitialInvestment,
  avgMonthlyContribution
}) => {
  // Format override percentages for display
  const formatOverrideRate = (gen: 1 | 2 | 3) => {
    return `${(overrideGenerations[gen] * 100).toFixed(1)}%`;
  };

  return (
    <div className="space-y-6 border-t-2 border-purple-200 pt-6 mt-6">
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-purple-800">Multi-Generation Overrides</h3>
            <div className="text-xs px-3 py-1 bg-purple-100 text-purple-700 rounded-full font-medium">Advanced</div>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-sm font-medium text-purple-700">Enable Multi-Gen</span>
            <Switch
              checked={enableMultiGen}
              onCheckedChange={setEnableMultiGen}
            />
          </div>
        </div>
        <p className="text-sm text-purple-600 mt-2">Track income from multiple generations of your downline organization</p>
      </div>

      {enableMultiGen && (
        <div className="space-y-6">
          {/* First Generation */}
          <div className="border-l-4 border-blue-400 pl-3 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">
                1st Generation Overrides
                <span className="ml-2 text-xs text-blue-600">({formatOverrideRate(1)} BP)</span>
              </h4>
            </div>

            {/* Number of 1st Generation Agents */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Number of 1st Gen Agents</label>
                <span className="text-xs font-medium">{genOneAgents}</span>
              </div>
              <Slider
                value={[genOneAgents]}
                min={0}
                max={15}
                step={1}
                onValueChange={(value) => setGenOneAgents(value[0])}
              />
            </div>

            {/* Average Rollover Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Rollover Cases per Agent</label>
                <span className="text-xs font-medium">{genOneRolloverCases}</span>
              </div>
              <Slider
                value={[genOneRolloverCases]}
                min={0}
                max={10}
                step={1}
                onValueChange={(value) => setGenOneRolloverCases(value[0])}
              />
              <div className="text-xs text-gray-500">
                Total: {genOneAgents * genOneRolloverCases} cases ({formatCurrency(genOneAgents * genOneRolloverCases * avgInitialInvestment)})
              </div>
            </div>

            {/* Average Monthly Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Monthly Cases per Agent</label>
                <span className="text-xs font-medium">{genOneMonthlyCases}</span>
              </div>
              <Slider
                value={[genOneMonthlyCases]}
                min={0}
                max={10}
                step={1}
                onValueChange={(value) => setGenOneMonthlyCases(value[0])}
              />
              <div className="text-xs text-gray-500">
                Total: {genOneAgents * genOneMonthlyCases} cases ({formatCurrency(genOneAgents * genOneMonthlyCases * avgMonthlyContribution)}/mo)
              </div>
            </div>
          </div>

          {/* Second Generation */}
          <div className="border-l-4 border-teal-400 pl-3 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">
                2nd Generation Overrides
                <span className="ml-2 text-xs text-teal-600">({formatOverrideRate(2)} BP)</span>
              </h4>
            </div>

            {/* Number of 2nd Generation Agents */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Number of 2nd Gen Agents</label>
                <span className="text-xs font-medium">{genTwoAgents}</span>
              </div>
              <Slider
                value={[genTwoAgents]}
                min={0}
                max={20}
                step={1}
                onValueChange={(value) => setGenTwoAgents(value[0])}
              />
            </div>

            {/* Average Rollover Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Rollover Cases per Agent</label>
                <span className="text-xs font-medium">{genTwoRolloverCases}</span>
              </div>
              <Slider
                value={[genTwoRolloverCases]}
                min={0}
                max={10}
                step={1}
                onValueChange={(value) => setGenTwoRolloverCases(value[0])}
              />
              <div className="text-xs text-gray-500">
                Total: {genTwoAgents * genTwoRolloverCases} cases ({formatCurrency(genTwoAgents * genTwoRolloverCases * avgInitialInvestment)})
              </div>
            </div>

            {/* Average Monthly Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Monthly Cases per Agent</label>
                <span className="text-xs font-medium">{genTwoMonthlyCases}</span>
              </div>
              <Slider
                value={[genTwoMonthlyCases]}
                min={0}
                max={10}
                step={1}
                onValueChange={(value) => setGenTwoMonthlyCases(value[0])}
              />
              <div className="text-xs text-gray-500">
                Total: {genTwoAgents * genTwoMonthlyCases} cases ({formatCurrency(genTwoAgents * genTwoMonthlyCases * avgMonthlyContribution)}/mo)
              </div>
            </div>
          </div>

          {/* Third Generation */}
          <div className="border-l-4 border-purple-400 pl-3 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">
                3rd Generation Overrides
                <span className="ml-2 text-xs text-purple-600">({formatOverrideRate(3)} BP)</span>
              </h4>
            </div>

            {/* Number of 3rd Generation Agents */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Number of 3rd Gen Agents</label>
                <span className="text-xs font-medium">{genThreeAgents}</span>
              </div>
              <Slider
                value={[genThreeAgents]}
                min={0}
                max={30}
                step={1}
                onValueChange={(value) => setGenThreeAgents(value[0])}
              />
            </div>

            {/* Average Rollover Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Rollover Cases per Agent</label>
                <span className="text-xs font-medium">{genThreeRolloverCases}</span>
              </div>
              <Slider
                value={[genThreeRolloverCases]}
                min={0}
                max={10}
                step={1}
                onValueChange={(value) => setGenThreeRolloverCases(value[0])}
              />
              <div className="text-xs text-gray-500">
                Total: {genThreeAgents * genThreeRolloverCases} cases ({formatCurrency(genThreeAgents * genThreeRolloverCases * avgInitialInvestment)})
              </div>
            </div>

            {/* Average Monthly Cases */}
            <div className="space-y-1">
              <div className="flex justify-between">
                <label className="text-sm">Avg Monthly Cases per Agent</label>
                <span className="text-xs font-medium">{genThreeMonthlyCases}</span>
              </div>
              <Slider
                value={[genThreeMonthlyCases]}
                min={0}
                max={10}
                step={1}
                onValueChange={(value) => setGenThreeMonthlyCases(value[0])}
              />
              <div className="text-xs text-gray-500">
                Total: {genThreeAgents * genThreeMonthlyCases} cases ({formatCurrency(genThreeAgents * genThreeMonthlyCases * avgMonthlyContribution)}/mo)
              </div>
            </div>
          </div>

          {/* Generation Summary */}
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-6 border border-gray-200">
            <h4 className="text-base font-semibold mb-4 text-gray-800">Multi-Generation Production Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white p-4 rounded-lg border border-blue-200">
                <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">1st Generation</p>
                <p className="text-lg font-bold text-blue-700">{genOneAgents} agents</p>
                <div className="text-sm text-gray-600 space-y-1 mt-2">
                  <p>{genOneAgents * genOneRolloverCases} rollover cases</p>
                  <p>{genOneAgents * genOneMonthlyCases} monthly cases</p>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border border-teal-200">
                <p className="text-xs font-medium text-teal-600 uppercase tracking-wide">2nd Generation</p>
                <p className="text-lg font-bold text-teal-700">{genTwoAgents} agents</p>
                <div className="text-sm text-gray-600 space-y-1 mt-2">
                  <p>{genTwoAgents * genTwoRolloverCases} rollover cases</p>
                  <p>{genTwoAgents * genTwoMonthlyCases} monthly cases</p>
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border border-purple-200">
                <p className="text-xs font-medium text-purple-600 uppercase tracking-wide">3rd Generation</p>
                <p className="text-lg font-bold text-purple-700">{genThreeAgents} agents</p>
                <div className="text-sm text-gray-600 space-y-1 mt-2">
                  <p>{genThreeAgents * genThreeRolloverCases} rollover cases</p>
                  <p>{genThreeAgents * genThreeMonthlyCases} monthly cases</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiGenSettings;
