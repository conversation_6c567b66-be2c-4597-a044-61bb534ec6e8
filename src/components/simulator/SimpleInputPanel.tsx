
import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SimpleSimulationInputs } from '@/types/simpleTypes';
import { formatCurrency, formatNumberWithCommas, parseFormattedNumber } from '@/utils/formatting';
import { Button } from '@/components/ui/button';
import { useColorTheme } from '@/contexts/ThemeContext';

interface SimpleInputPanelProps {
  inputs: SimpleSimulationInputs;
  onInputChange: (key: keyof SimpleSimulationInputs, value: number) => void;
}

const SimpleInputPanel: React.FC<SimpleInputPanelProps> = ({ inputs, onInputChange }) => {
  const { colorTheme } = useColorTheme();

  // State for formatted initial investment input
  const [formattedInitialInvestment, setFormattedInitialInvestment] = useState(formatNumberWithCommas(inputs.initialInvestment));
  const [isInitialInvestmentFocused, setIsInitialInvestmentFocused] = useState(false);

  // State for formatted PAC per case input
  const [formattedPacPerCase, setFormattedPacPerCase] = useState(formatNumberWithCommas(inputs.pacPerCase));
  const [isPacPerCaseFocused, setIsPacPerCaseFocused] = useState(false);

  // Update formatted values when inputs change (but not when focused)
  useEffect(() => {
    if (!isInitialInvestmentFocused) {
      setFormattedInitialInvestment(formatNumberWithCommas(inputs.initialInvestment));
    }
  }, [inputs.initialInvestment, isInitialInvestmentFocused]);

  useEffect(() => {
    if (!isPacPerCaseFocused) {
      setFormattedPacPerCase(formatNumberWithCommas(inputs.pacPerCase));
    }
  }, [inputs.pacPerCase, isPacPerCaseFocused]);

  const handleReset = () => {
    // Reset to defaults
    onInputChange('initialInvestment', 25000);
    setFormattedInitialInvestment(formatNumberWithCommas(25000));
    onInputChange('numRolloverCases', 5);
    onInputChange('pacPerCase', 250);
    setFormattedPacPerCase(formatNumberWithCommas(250));
    onInputChange('numPacCases', 5);
    onInputChange('projectionYears', 1);
    onInputChange('marketGrowth', 0.10);
    onInputChange('numRLs', 3);
    onInputChange('businessOwnerAgencies', 2);
    onInputChange('businessOwnerRLsPerAgency', 3);
    onInputChange('branchOfficeAgencies', 3);
    onInputChange('branchOfficeRLsPerAgency', 4);
  };

  // Handle formatted initial investment input
  const handleInitialInvestmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Allow only numbers and commas
    const cleanValue = newValue.replace(/[^0-9,]/g, '');

    // Parse the number to validate it
    const numValue = cleanValue === '' ? 0 : parseFormattedNumber(cleanValue);

    if (!isNaN(numValue)) {
      // Format with commas for display
      const formattedValue = cleanValue === '' ? '' : formatNumberWithCommas(numValue);
      setFormattedInitialInvestment(formattedValue);
      onInputChange('initialInvestment', numValue);
    }
  };

  const handleInitialInvestmentFocus = () => {
    setIsInitialInvestmentFocused(true);
    // Remove commas when focused for easier editing
    const numValue = parseFormattedNumber(formattedInitialInvestment);
    if (!isNaN(numValue) && numValue !== 0) {
      setFormattedInitialInvestment(numValue.toString());
    } else if (numValue === 0) {
      setFormattedInitialInvestment('');
    }
  };

  const handleInitialInvestmentBlur = () => {
    setIsInitialInvestmentFocused(false);
    const numValue = parseFormattedNumber(formattedInitialInvestment);
    if (isNaN(numValue)) {
      setFormattedInitialInvestment('0');
      onInputChange('initialInvestment', 0);
    } else {
      // Format with commas when not focused
      setFormattedInitialInvestment(formatNumberWithCommas(numValue));
      onInputChange('initialInvestment', numValue);
    }
  };

  // Handle formatted PAC per case input
  const handlePacPerCaseChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Allow only numbers and commas
    const cleanValue = newValue.replace(/[^0-9,]/g, '');

    // Parse the number to validate it
    const numValue = cleanValue === '' ? 0 : parseFormattedNumber(cleanValue);

    if (!isNaN(numValue)) {
      // Format with commas for display
      const formattedValue = cleanValue === '' ? '' : formatNumberWithCommas(numValue);
      setFormattedPacPerCase(formattedValue);
      onInputChange('pacPerCase', numValue);
    }
  };

  const handlePacPerCaseFocus = () => {
    setIsPacPerCaseFocused(true);
    // Remove commas when focused for easier editing
    const numValue = parseFormattedNumber(formattedPacPerCase);
    if (!isNaN(numValue) && numValue !== 0) {
      setFormattedPacPerCase(numValue.toString());
    } else if (numValue === 0) {
      setFormattedPacPerCase('');
    }
  };

  const handlePacPerCaseBlur = () => {
    setIsPacPerCaseFocused(false);
    const numValue = parseFormattedNumber(formattedPacPerCase);
    if (isNaN(numValue)) {
      setFormattedPacPerCase('0');
      onInputChange('pacPerCase', 0);
    } else {
      // Format with commas when not focused
      setFormattedPacPerCase(formatNumberWithCommas(numValue));
      onInputChange('pacPerCase', numValue);
    }
  };

  // Generate options for dropdowns
  const generateOptions = (max: number) => {
    return Array.from({ length: max }, (_, i) => i + 1);
  };

  return (
    <Card className={colorTheme === 'wealthtech' ? 'bg-wealthtech-tile-bg/80' : 'bg-primerica-tile-bg/80'}>
      <CardHeader className="pb-2">
        <CardTitle className="flex justify-between items-center">
          <span>Income Simulator Settings</span>
          <Button variant="ghost" size="sm" onClick={handleReset}>Reset to Default</Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-6 grid-cols-1 md:grid-cols-3">
          {/* Left Column - Money-related fields */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="initialInvestment">Initial Rollover Per Case ($)</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="initialInvestment"
                  type="text"
                  value={formattedInitialInvestment}
                  onChange={handleInitialInvestmentChange}
                  onFocus={handleInitialInvestmentFocus}
                  onBlur={handleInitialInvestmentBlur}
                  className="flex-1"
                  placeholder="0"
                />
              </div>
              <p className="text-xs text-muted-foreground">Default: $25,000</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="pacPerCase">Monthly PAC Per Case ($)</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="pacPerCase"
                  type="text"
                  value={formattedPacPerCase}
                  onChange={handlePacPerCaseChange}
                  onFocus={handlePacPerCaseFocus}
                  onBlur={handlePacPerCaseBlur}
                  className="flex-1"
                  placeholder="0"
                />
              </div>
              <p className="text-xs text-muted-foreground">Default: $250/month</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="marketGrowth">Annual Market Growth</Label>
              <Slider
                id="marketGrowth"
                min={3}
                max={20}
                step={0.5}
                value={[inputs.marketGrowth * 100]}
                onValueChange={(values) => onInputChange('marketGrowth', values[0] / 100)}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>3%</span>
                <span className={inputs.marketGrowth > 0.11 ? "text-red-500 font-medium" : "text-green-500 font-medium"}>
                  {(inputs.marketGrowth * 100).toFixed(1)}%
                </span>
                <span>20%</span>
              </div>
            </div>
          </div>

          {/* Middle Column - Case numbers */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="numRolloverCases">Number of Rollover Cases</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="numRolloverCases"
                  type="number"
                  value={inputs.numRolloverCases}
                  onChange={(e) => onInputChange('numRolloverCases', parseInt(e.target.value) || 0)}
                  className="flex-1"
                />
              </div>
              <p className="text-xs text-muted-foreground">Default: 5 cases</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="numPacCases">Number of PAC Cases</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="numPacCases"
                  type="number"
                  value={inputs.numPacCases}
                  onChange={(e) => onInputChange('numPacCases', parseInt(e.target.value) || 0)}
                  className="flex-1"
                />
              </div>
              <p className="text-xs text-muted-foreground">Default: 5 cases</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="projectionYears">Years of Growth</Label>
              <Slider
                id="projectionYears"
                min={1}
                max={30}
                step={1}
                value={[inputs.projectionYears]}
                onValueChange={(values) => onInputChange('projectionYears', values[0])}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>1 year</span>
                <span>{inputs.projectionYears} years</span>
                <span>30 years</span>
              </div>
            </div>
          </div>

          {/* Right Column - Expansion Office-related fields */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="numRLs">Licensed Reps on Team</Label>
              <Input
                id="numRLs"
                type="number"
                value={inputs.numRLs}
                onChange={(e) => onInputChange('numRLs', parseInt(e.target.value) || 0)}
                className="flex-1"
              />
              <p className="text-xs text-muted-foreground">Default: 3 Licensed Reps</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="businessOwnerAgencies">Business Owner Expansion Offices</Label>
              <Input
                id="businessOwnerAgencies"
                type="number"
                value={inputs.businessOwnerAgencies}
                onChange={(e) => onInputChange('businessOwnerAgencies', parseInt(e.target.value) || 0)}
                className="flex-1"
              />
              <p className="text-xs text-muted-foreground">Default: 2 Expansion Offices</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="businessOwnerRLsPerAgency">Business Owner Licensed Reps/Expansion Office</Label>
              <Input
                id="businessOwnerRLsPerAgency"
                type="number"
                value={inputs.businessOwnerRLsPerAgency}
                onChange={(e) => onInputChange('businessOwnerRLsPerAgency', parseInt(e.target.value) || 0)}
                className="flex-1"
              />
              <p className="text-xs text-muted-foreground">Default: 3 Licensed Reps per Expansion Office</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="branchOfficeAgencies">Branch Office Expansion Offices</Label>
              <Input
                id="branchOfficeAgencies"
                type="number"
                value={inputs.branchOfficeAgencies}
                onChange={(e) => onInputChange('branchOfficeAgencies', parseInt(e.target.value) || 0)}
                className="flex-1"
              />
              <p className="text-xs text-muted-foreground">Default: 3 Expansion Offices</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="branchOfficeRLsPerAgency">Branch Office Licensed Reps/Expansion Office</Label>
              <Input
                id="branchOfficeRLsPerAgency"
                type="number"
                value={inputs.branchOfficeRLsPerAgency}
                onChange={(e) => onInputChange('branchOfficeRLsPerAgency', parseInt(e.target.value) || 0)}
                className="flex-1"
              />
              <p className="text-xs text-muted-foreground">Default: 4 Licensed Reps per Expansion Office</p>
            </div>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm font-medium">Total Monthly Investment: </span>
              <span className="text-sm">{formatCurrency(inputs.initialInvestment * inputs.numRolloverCases)}</span>
            </div>
            <div>
              <span className="text-sm font-medium">Total Monthly PAC: </span>
              <span className="text-sm">{formatCurrency(inputs.pacPerCase * inputs.numPacCases)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SimpleInputPanel;
