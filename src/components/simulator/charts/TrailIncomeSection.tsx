
import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import TrailIncomeChart from '../TrailIncomeChart';
import YearlyTrailReport from '../YearlyTrailReport';
import CommissionBreakdownChart from '../ResultComponents/CommissionBreakdownChart';
import TrailYearHighlight from '../ResultComponents/TrailYearHighlight';

interface TrailIncomeSectionProps {
  results: {
    personalCommission: number;
    overrideCommission: number;
    totalCommission: number;
    trailProjections?: {
      year: number;
      aum: number;
      personalTrail: number;
      overrideTrail: number;
      totalTrail: number;
      monthlyTrail?: number;
      generationOverrides?: {
        gen1: number;
        gen2: number;
        gen3: number;
        total: number;
      };
    }[];
    generationCommissions?: {
      gen1: number;
      gen2: number;
      gen3: number;
      total: number;
    };
  };
  viewMode: 'agent' | 'broker';
  enableMultiGen?: boolean;
  selectedYearTrail: any; // Using any temporarily for brevity
}

const TrailIncomeSection: React.FC<TrailIncomeSectionProps> = ({
  results,
  viewMode,
  enableMultiGen,
  selectedYearTrail
}) => {
  if (!results.trailProjections || results.trailProjections.length === 0) {
    return null;
  }

  return (
    <div className="space-y-8">
      {selectedYearTrail && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
          <TrailYearHighlight
            trailProjection={selectedYearTrail}
            viewMode={viewMode}
          />
        </div>
      )}

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-gray-900">Trail Income Projection</h3>
          <span className="text-sm text-gray-500">Long-term income growth</span>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
          <div className="h-80">
            <TrailIncomeChart
              trailProjections={results.trailProjections}
              viewMode={viewMode}
              enableMultiGen={enableMultiGen}
              selectedYear={selectedYearTrail?.year || 5}
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-gray-900">Commission Breakdown</h3>
          <span className="text-sm text-gray-500">Income distribution</span>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
          <CommissionBreakdownChart
            data={{
              personalCommission: results.personalCommission,
              overrideCommission: results.overrideCommission,
              generationCommissions: results.generationCommissions
            }}
            viewMode={viewMode}
            enableMultiGen={enableMultiGen}
          />
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="yearly-report" className="border-0">
            <AccordionTrigger className="px-6 py-4 text-lg font-semibold text-gray-900 hover:no-underline hover:bg-gray-50">
              📊 Detailed Yearly Trail Income Report
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-6">
              <YearlyTrailReport
                trailProjections={results.trailProjections}
                viewMode={viewMode}
                enableMultiGen={enableMultiGen}
              />
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
};

export default TrailIncomeSection;
