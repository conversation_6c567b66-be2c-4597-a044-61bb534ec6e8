
import React from 'react';
import { formatCurrency } from '@/utils/formatting';

interface YearlyTrailReportProps {
  trailProjections: {
    year: number;
    aum: number;
    personalTrail: number;
    overrideTrail: number;
    totalTrail: number;
    monthlyTrail?: number;
    generationOverrides?: {
      gen1: number;
      gen2: number;
      gen3: number;
      total: number;
    };
  }[];
  viewMode: 'agent' | 'broker';
  enableMultiGen?: boolean;
}

const YearlyTrailReport: React.FC<YearlyTrailReportProps> = ({
  trailProjections,
  viewMode,
  enableMultiGen
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Year-by-Year Trail Income Report</h3>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {trailProjections.map((projection) => (
          <div key={projection.year} className="p-4 bg-financial-grey rounded-md shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold">Year {projection.year}</h4>
              {projection.year <= 5 && (
                <span className="text-xs px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full">
                  {projection.year === 1 ? 'First' : projection.year === 5 ? 'Five Year' : ''}
                </span>
              )}
            </div>

            <div className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Assets Under Management</p>
                <p className="text-lg font-semibold">{formatCurrency(projection.aum)}</p>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Annual Trail Income</p>
                <p className="text-md font-medium text-financial-green">
                  {formatCurrency(projection.totalTrail)}
                </p>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Monthly Trail Income</p>
                <p className="text-md font-medium text-financial-green">
                  {formatCurrency(projection.monthlyTrail || projection.totalTrail / 12)}
                </p>
              </div>

              {viewMode === 'broker' && (
                <div className="pt-1 border-t border-gray-200">
                  <p className="text-sm text-muted-foreground">Direct Override</p>
                  <p className="text-sm text-financial-blue">
                    {formatCurrency(projection.overrideTrail)}
                  </p>
                </div>
              )}

              {viewMode === 'broker' && enableMultiGen && projection.generationOverrides && (
                <div className="pt-1 border-t border-gray-200">
                  <p className="text-sm text-muted-foreground">Generation Overrides</p>
                  <div className="grid grid-cols-3 gap-1 text-sm mt-1">
                    <div>
                      <p className="text-xs text-muted-foreground">Gen 1</p>
                      <p className="text-xs text-financial-lightBlue">{formatCurrency(projection.generationOverrides.gen1)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Gen 2</p>
                      <p className="text-xs text-financial-lightBlue">{formatCurrency(projection.generationOverrides.gen2)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Gen 3</p>
                      <p className="text-xs text-financial-lightBlue">{formatCurrency(projection.generationOverrides.gen3)}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default YearlyTrailReport;
