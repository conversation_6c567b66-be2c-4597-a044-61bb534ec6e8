
import React from 'react';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { LayoutDashboard, Timer, SplitSquareVertical } from 'lucide-react';
import { useColorTheme } from '@/contexts/ThemeContext';
import { motion } from 'framer-motion';

export type ResultViewMode = 'cards' | 'timeline' | 'split';

interface ResultViewToggleProps {
  viewMode: ResultViewMode;
  onViewModeChange: (mode: ResultViewMode) => void;
}

const ResultViewToggle: React.FC<ResultViewToggleProps> = ({ 
  viewMode, 
  onViewModeChange 
}) => {
  const { colorTheme } = useColorTheme();
  
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-700">View Mode</h3>
        <span className="text-xs text-gray-500">Choose your preferred layout</span>
      </div>
      <ToggleGroup
        type="single"
        value={viewMode}
        onValueChange={(value) => {
          if (value) onViewModeChange(value as ResultViewMode);
        }}
        className="grid grid-cols-3 gap-2 w-full"
      >
        <ToggleGroupItem
          value="cards"
          className="flex-1 data-[state=on]:bg-blue-500 data-[state=on]:text-white data-[state=off]:bg-gray-50 data-[state=off]:text-gray-600 data-[state=off]:hover:bg-gray-100 transition-all duration-200 border border-gray-200 rounded-lg p-3"
          aria-label="Cards view"
        >
          <motion.span
            className="flex flex-col items-center justify-center gap-1"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <LayoutDashboard size={20} />
            <span className="text-xs font-medium">Cards</span>
          </motion.span>
        </ToggleGroupItem>
        
        <ToggleGroupItem
          value="timeline"
          className="flex-1 data-[state=on]:bg-green-500 data-[state=on]:text-white data-[state=off]:bg-gray-50 data-[state=off]:text-gray-600 data-[state=off]:hover:bg-gray-100 transition-all duration-200 border border-gray-200 rounded-lg p-3"
          aria-label="Timeline view"
        >
          <motion.span
            className="flex flex-col items-center justify-center gap-1"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Timer size={20} />
            <span className="text-xs font-medium">Timeline</span>
          </motion.span>
        </ToggleGroupItem>

        <ToggleGroupItem
          value="split"
          className="flex-1 data-[state=on]:bg-purple-500 data-[state=on]:text-white data-[state=off]:bg-gray-50 data-[state=off]:text-gray-600 data-[state=off]:hover:bg-gray-100 transition-all duration-200 border border-gray-200 rounded-lg p-3"
          aria-label="Split view"
        >
          <motion.span
            className="flex flex-col items-center justify-center gap-1"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <SplitSquareVertical size={20} />
            <span className="text-xs font-medium">Split</span>
          </motion.span>
        </ToggleGroupItem>
      </ToggleGroup>
    </div>
  );
};

export default ResultViewToggle;
