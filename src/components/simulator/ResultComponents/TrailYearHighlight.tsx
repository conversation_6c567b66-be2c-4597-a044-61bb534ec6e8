import React from 'react';
import { formatCurrency } from '@/utils/formatting';
interface TrailYearHighlightProps {
  trailProjection: {
    year: number;
    aum: number;
    personalTrail: number;
    overrideTrail: number;
    totalTrail: number;
    monthlyTrail?: number;
    generationOverrides?: {
      gen1: number;
      gen2: number;
      gen3: number;
      total: number;
    };
  };
  viewMode: 'agent' | 'broker';
}
const TrailYearHighlight: React.FC<TrailYearHighlightProps> = ({
  trailProjection,
  viewMode
}) => {
  return <div className="p-4 bg-financial-green bg-opacity-10 rounded-lg border border-financial-green border-opacity-30">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-slate-950">Year {trailProjection.year} Trail Income</h3>
        <span className="px-2 py-0.5 bg-financial-green bg-opacity-20 text-xs rounded-full text-slate-950">
          Selected Projection
        </span>
      </div>
      <div className="grid grid-cols-3 gap-3">
        <div>
          <p className="text-sm text-muted-foreground px-0 text-left">Assets Under Management</p>
          <p className="text-xl font-semibold">{formatCurrency(trailProjection.aum)}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Annual Trail Income</p>
          <p className="text-xl font-semibold text-slate-950">
            {formatCurrency(trailProjection.totalTrail)}
          </p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Monthly Trail Income</p>
          <p className="text-xl font-semibold text-slate-950">
            {formatCurrency(trailProjection.monthlyTrail || trailProjection.totalTrail / 12)}
          </p>
        </div>
      </div>
      {viewMode === 'broker' && trailProjection.generationOverrides && <div className="mt-3 pt-3 border-t border-financial-green border-opacity-20">
          <p className="text-sm text-muted-foreground mb-2">Generation Override Trails (Year {trailProjection.year})</p>
          <div className="grid grid-cols-3 gap-2">
            <div>
              <p className="text-xs text-muted-foreground">1st Generation</p>
              <p className="text-sm font-medium text-financial-blue">
                {formatCurrency(trailProjection.generationOverrides.gen1)}
              </p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">2nd Generation</p>
              <p className="text-sm font-medium text-financial-blue">
                {formatCurrency(trailProjection.generationOverrides.gen2)}
              </p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">3rd Generation</p>
              <p className="text-sm font-medium text-financial-blue">
                {formatCurrency(trailProjection.generationOverrides.gen3)}
              </p>
            </div>
          </div>
        </div>}
    </div>;
};
export default TrailYearHighlight;