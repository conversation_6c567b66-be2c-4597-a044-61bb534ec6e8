
import React, { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Bug } from 'lucide-react';
import { formatCurrency, formatPercentage } from '@/utils/formatting';

interface ResultsCardProps {
  title?: string;
  debugMode?: boolean;
  cdrRate?: number;
  cdrValue?: number;
  children: ReactNode;
}

const ResultsCard: React.FC<ResultsCardProps> = ({
  title = "Commission Results",
  debugMode = false,
  cdrRate,
  cdrValue,
  children
}) => {
  return (
    <Card className="bg-white shadow-lg border-0">
      {title && (
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900">{title}</CardTitle>
          {debugMode && (
            <div className="flex items-center space-x-2 mt-3 px-3 py-2 bg-yellow-50 border border-yellow-200 rounded-lg">
              <Bug size={16} className="text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">Debug Mode Active</span>
            </div>
          )}
        </CardHeader>
      )}
      <CardContent className="space-y-8 p-6">
        {children}
      </CardContent>

      {(cdrRate !== undefined && cdrValue !== undefined) && (
        <CardFooter className="bg-gray-50 border-t border-gray-200 px-6 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span className="font-medium">CDR Rate: <span className="text-blue-600">{formatPercentage(cdrRate)}</span></span>
              <span className="hidden sm:inline text-gray-400">•</span>
              <span className="font-medium">CDR Value: <span className="text-green-600">{formatCurrency(cdrValue)}</span></span>
            </div>
            <span className="text-xs text-gray-500 sm:ml-auto">Based on investment range and fund type cap</span>
          </div>
        </CardFooter>
      )}
    </Card>
  );
};

export default ResultsCard;
