
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Settings, X, Bug } from 'lucide-react';
import { useColorTheme } from '@/contexts/ThemeContext';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
// Simple mobile detection hook
const useIsMobile = () => {
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
};

const SettingsPanel = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { debugMode, setDebugMode } = useColorTheme();
  const isMobile = useIsMobile();
  const togglePanel = () => setIsOpen(!isOpen);
  const MotionButton = motion.button;

  return (
    <>
      {/* Settings Toggle Button */}
      <Tooltip>
        <TooltipTrigger asChild>
          <MotionButton
            onClick={togglePanel}
            className="p-2 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Settings className="w-5 h-5" />
          </MotionButton>
        </TooltipTrigger>
        <TooltipContent>
          <p>Settings</p>
        </TooltipContent>
      </Tooltip>

      {/* Settings Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className={`fixed top-0 right-0 z-40 h-full ${isMobile ? 'w-full' : 'w-80'} glass border-l border-white/20 dark:border-white/10 shadow-xl overflow-y-auto`}
          >
            <div className="p-6 space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-display font-semibold">Settings</h2>
                <motion.button
                  onClick={togglePanel}
                  className="p-2 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <X className="w-5 h-5" />
                </motion.button>
              </div>

              {/* Debug Mode Toggle */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Bug className="w-4 h-4 text-gray-500" />
                    <h3 className="text-sm font-medium">Debug Mode</h3>
                  </div>
                  <Switch
                    checked={debugMode}
                    onCheckedChange={setDebugMode}
                  />
                </div>
                <p className="text-xs text-gray-500">
                  Show calculation breakdowns and additional developer information
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default SettingsPanel;
