import { formatCurrency, formatPercentage, formatNumber, formatNumberWithCommas, parseFormattedNumber } from '@/utils/formatting';

describe('Formatting Utilities', () => {
  describe('formatCurrency', () => {
    it('should format currency with default settings', () => {
      expect(formatCurrency(1234.56)).toBe('$1,235');
    });

    it('should format currency with custom decimal places', () => {
      expect(formatCurrency(1234.56, 'USD', 'en-US', 2)).toBe('$1,234.56');
    });

    it('should handle zero values', () => {
      expect(formatCurrency(0)).toBe('$0');
    });

    it('should handle negative values', () => {
      expect(formatCurrency(-1234.56)).toBe('-$1,235');
    });

    it('should handle large numbers', () => {
      expect(formatCurrency(1234567.89)).toBe('$1,234,568');
    });

    it('should format with different currencies', () => {
      expect(formatCurrency(1234.56, 'EUR', 'en-US', 2)).toBe('€1,234.56');
    });
  });

  describe('formatPercentage', () => {
    it('should format percentage with default settings', () => {
      expect(formatPercentage(0.1234)).toBe('12.34%');
    });

    it('should format percentage with custom decimal places', () => {
      expect(formatPercentage(0.1234, 'en-US', 1)).toBe('12.3%');
    });

    it('should handle zero values', () => {
      expect(formatPercentage(0)).toBe('0.00%');
    });

    it('should handle values greater than 1', () => {
      expect(formatPercentage(1.5)).toBe('150.00%');
    });
  });

  describe('formatNumber', () => {
    it('should format numbers with commas', () => {
      expect(formatNumber(1234567)).toBe('1,234,567');
    });

    it('should handle decimal places', () => {
      expect(formatNumber(1234.567)).toBe('1,234.567');
    });

    it('should handle zero', () => {
      expect(formatNumber(0)).toBe('0');
    });
  });

  describe('formatNumberWithCommas', () => {
    it('should format numbers with commas (no decimals)', () => {
      expect(formatNumberWithCommas(1234567)).toBe('1,234,567');
    });

    it('should handle zero', () => {
      expect(formatNumberWithCommas(0)).toBe('0');
    });

    it('should handle large numbers', () => {
      expect(formatNumberWithCommas(25000)).toBe('25,000');
    });

    it('should handle small numbers', () => {
      expect(formatNumberWithCommas(250)).toBe('250');
    });
  });

  describe('parseFormattedNumber', () => {
    it('should parse formatted numbers with commas', () => {
      expect(parseFormattedNumber('1,234,567')).toBe(1234567);
    });

    it('should parse numbers without commas', () => {
      expect(parseFormattedNumber('25000')).toBe(25000);
    });

    it('should handle empty string', () => {
      expect(parseFormattedNumber('')).toBe(0);
    });

    it('should handle invalid input', () => {
      expect(parseFormattedNumber('abc')).toBeNaN();
    });

    it('should handle mixed valid/invalid input', () => {
      expect(parseFormattedNumber('1,234abc')).toBeNaN();
    });
  });
});
