import { calculateSimpleScenarios } from '@/utils/simpleCalculator';
import { SimpleSimulationInputs } from '@/types/simpleTypes';
import { vi } from 'vitest';

// Mock the trailsLogic module
vi.mock('../../simConfig/trailsLogic', () => ({
  trailsLogic: {
    calculateProjectedAUMWithContributions: vi.fn(() => 1000000)
  }
}));

describe('Simple Calculator', () => {
  const mockInputs: SimpleSimulationInputs = {
    initialInvestment: 25000,
    numRolloverCases: 5,
    pacPerCase: 250,
    numPacCases: 5,
    projectionYears: 10,
    marketGrowth: 0.10,
    numRLs: 3,
    numAgencies: 2,
    numRLsPerAgency: 3
  };

  it('should calculate all scenario results', () => {
    const results = calculateSimpleScenarios(mockInputs);

    expect(results).toBeDefined();
    expect(results.selfEmployed).toBeDefined();
    expect(results.selfEmployedTeam).toBeDefined();
    expect(results.businessOwner).toBeDefined();
    expect(results.passiveOwner).toBeDefined();
    expect(results.aum).toBeDefined();
  });

  it('should calculate self-employed scenario correctly', () => {
    const results = calculateSimpleScenarios(mockInputs);

    expect(results.selfEmployed.directEffort).toBeGreaterThan(0);
    expect(results.selfEmployed.recurringPac).toBeGreaterThan(0);
    expect(results.selfEmployed.trailIncome).toBeGreaterThan(0);
    expect(results.selfEmployed.total).toBeGreaterThan(0);
  });

  it('should calculate team scenario with override income', () => {
    const results = calculateSimpleScenarios(mockInputs);

    expect(results.selfEmployedTeam.teamOverride).toBeGreaterThan(0);
    expect(results.selfEmployedTeam.total).toBeGreaterThan(results.selfEmployed.total);
  });

  it('should calculate business owner scenario', () => {
    const results = calculateSimpleScenarios(mockInputs);

    expect(results.businessOwner.personalOffice).toBeDefined();
    expect(results.businessOwner.agencyOverrides).toBeDefined();
    expect(results.businessOwner.total).toBeGreaterThan(0);
  });

  it('should calculate passive owner scenario', () => {
    const results = calculateSimpleScenarios(mockInputs);

    expect(results.passiveOwner.recurringPac).toBeGreaterThan(0);
    expect(results.passiveOwner.trailIncome).toBeGreaterThan(0);
    expect(results.passiveOwner.agencyOverrides).toBeDefined();
    expect(results.passiveOwner.total).toBeGreaterThan(0);
  });

  it('should calculate AUM projections', () => {
    const results = calculateSimpleScenarios(mockInputs);

    expect(results.aum.personal).toBeGreaterThan(0);
    expect(results.aum.team).toBeGreaterThan(0);
    expect(results.aum.agencies).toBeGreaterThan(0);
    expect(results.aum.total).toBeGreaterThan(0);
  });

  it('should handle edge cases with zero inputs', () => {
    const zeroInputs: SimpleSimulationInputs = {
      ...mockInputs,
      initialInvestment: 0,
      numRolloverCases: 0,
      pacPerCase: 0,
      numPacCases: 0
    };

    const results = calculateSimpleScenarios(zeroInputs);

    expect(results).toBeDefined();
    expect(results.selfEmployed.directEffort).toBe(0);
    expect(results.selfEmployed.recurringPac).toBe(0);
  });

  it('should scale results with input changes', () => {
    const doubledInputs: SimpleSimulationInputs = {
      ...mockInputs,
      initialInvestment: 50000,
      numRolloverCases: 10
    };

    const originalResults = calculateSimpleScenarios(mockInputs);
    const doubledResults = calculateSimpleScenarios(doubledInputs);

    expect(doubledResults.selfEmployed.directEffort).toBeGreaterThan(
      originalResults.selfEmployed.directEffort
    );
  });
});
