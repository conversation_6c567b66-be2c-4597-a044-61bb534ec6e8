// React Performance Monitor
// Run this in Chrome DevTools Console to monitor React-specific performance issues

console.log('⚛️ React Performance Monitor Starting...\n');

// 1. Monitor React Re-renders
function monitorReactRenders() {
  console.log('🔄 REACT RENDER MONITORING');
  console.log('==========================');
  
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('✅ React DevTools detected');
    
    // Hook into React's render cycle
    const originalRender = window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot;
    let renderCount = 0;
    let renderTimes = [];
    
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = function(id, root, priorityLevel) {
      renderCount++;
      const renderTime = performance.now();
      renderTimes.push(renderTime);
      
      // Log excessive renders
      if (renderCount > 10) {
        console.warn(`⚠️ High render count detected: ${renderCount} renders`);
      }
      
      // Call original function
      if (originalRender) {
        originalRender.call(this, id, root, priorityLevel);
      }
    };
    
    // Report render stats after 5 seconds
    setTimeout(() => {
      console.log(`Total renders in 5s: ${renderCount}`);
      if (renderCount > 20) {
        console.warn('⚠️ Excessive re-renders detected - check component dependencies');
      }
    }, 5000);
    
  } else {
    console.log('❌ React DevTools not available');
  }
  console.log('');
}

// 2. Monitor State Updates
function monitorStateUpdates() {
  console.log('📊 STATE UPDATE MONITORING');
  console.log('==========================');
  
  // Monitor input changes (potential state triggers)
  let inputChangeCount = 0;
  document.addEventListener('input', () => {
    inputChangeCount++;
  });
  
  // Monitor click events (potential state triggers)
  let clickCount = 0;
  document.addEventListener('click', () => {
    clickCount++;
  });
  
  // Report after 10 seconds
  setTimeout(() => {
    console.log(`Input changes in 10s: ${inputChangeCount}`);
    console.log(`Clicks in 10s: ${clickCount}`);
    
    if (inputChangeCount > 50) {
      console.warn('⚠️ High input activity - check for unnecessary re-renders on input');
    }
  }, 10000);
  
  console.log('Monitoring input and click events...');
  console.log('');
}

// 3. Monitor Memory Leaks
function monitorMemoryLeaks() {
  console.log('🧠 MEMORY LEAK MONITORING');
  console.log('=========================');
  
  const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
  
  // Check memory every 5 seconds
  const memoryInterval = setInterval(() => {
    if (performance.memory) {
      const currentMemory = performance.memory.usedJSHeapSize;
      const memoryIncrease = currentMemory - initialMemory;
      const memoryMB = (memoryIncrease / 1024 / 1024).toFixed(2);
      
      console.log(`Memory increase: ${memoryMB} MB`);
      
      if (memoryIncrease > 50 * 1024 * 1024) { // 50MB increase
        console.warn('⚠️ Significant memory increase detected - potential memory leak');
      }
    }
  }, 5000);
  
  // Stop monitoring after 30 seconds
  setTimeout(() => {
    clearInterval(memoryInterval);
    console.log('Memory monitoring stopped');
  }, 30000);
  
  console.log('Monitoring memory usage for 30 seconds...');
  console.log('');
}

// 4. Monitor Animation Performance
function monitorAnimationPerformance() {
  console.log('🎬 ANIMATION PERFORMANCE MONITORING');
  console.log('===================================');
  
  let frameCount = 0;
  let lastTime = performance.now();
  let fps = [];
  
  function measureFPS() {
    const currentTime = performance.now();
    const delta = currentTime - lastTime;
    const currentFPS = 1000 / delta;
    
    fps.push(currentFPS);
    frameCount++;
    lastTime = currentTime;
    
    // Keep only last 60 frames
    if (fps.length > 60) {
      fps.shift();
    }
    
    // Log low FPS
    if (currentFPS < 30 && frameCount > 10) {
      console.warn(`⚠️ Low FPS detected: ${currentFPS.toFixed(1)} fps`);
    }
    
    requestAnimationFrame(measureFPS);
  }
  
  requestAnimationFrame(measureFPS);
  
  // Report average FPS after 10 seconds
  setTimeout(() => {
    const avgFPS = fps.reduce((a, b) => a + b, 0) / fps.length;
    console.log(`Average FPS over last 60 frames: ${avgFPS.toFixed(1)}`);
    
    if (avgFPS < 45) {
      console.warn('⚠️ Low average FPS - check for expensive animations or renders');
    }
  }, 10000);
  
  console.log('Monitoring FPS...');
  console.log('');
}

// 5. Monitor Component Mount/Unmount
function monitorComponentLifecycle() {
  console.log('🔄 COMPONENT LIFECYCLE MONITORING');
  console.log('=================================');
  
  // Monitor DOM mutations (component mounts/unmounts)
  let mutationCount = 0;
  const observer = new MutationObserver((mutations) => {
    mutationCount += mutations.length;
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true
  });
  
  // Report after 10 seconds
  setTimeout(() => {
    console.log(`DOM mutations in 10s: ${mutationCount}`);
    
    if (mutationCount > 100) {
      console.warn('⚠️ High DOM mutation rate - check for unnecessary component re-mounts');
    }
    
    observer.disconnect();
  }, 10000);
  
  console.log('Monitoring DOM mutations...');
  console.log('');
}

// 6. Specific Fund-Wise Simulator Monitoring
function monitorSimulatorSpecifics() {
  console.log('💰 FUND-WISE SIMULATOR MONITORING');
  console.log('=================================');
  
  // Monitor slider interactions
  let sliderInteractions = 0;
  document.addEventListener('input', (e) => {
    if (e.target.type === 'range' || e.target.getAttribute('role') === 'slider') {
      sliderInteractions++;
    }
  });
  
  // Monitor calculation triggers
  let calculationTriggers = 0;
  const originalConsoleLog = console.log;
  console.log = function(...args) {
    if (args.some(arg => typeof arg === 'string' && arg.includes('calculation'))) {
      calculationTriggers++;
    }
    originalConsoleLog.apply(console, args);
  };
  
  // Report after 15 seconds
  setTimeout(() => {
    console.log(`Slider interactions: ${sliderInteractions}`);
    console.log(`Calculation triggers: ${calculationTriggers}`);
    
    if (sliderInteractions > 20) {
      console.warn('⚠️ High slider interaction rate - consider debouncing calculations');
    }
    
    // Restore console.log
    console.log = originalConsoleLog;
  }, 15000);
  
  console.log('Monitoring simulator-specific interactions...');
  console.log('');
}

// Run all monitoring
function startMonitoring() {
  monitorReactRenders();
  monitorStateUpdates();
  monitorMemoryLeaks();
  monitorAnimationPerformance();
  monitorComponentLifecycle();
  monitorSimulatorSpecifics();
  
  console.log('🎯 ALL MONITORS STARTED');
  console.log('=======================');
  console.log('Monitoring will run for various durations.');
  console.log('Check console for real-time performance warnings.');
  console.log('Use React DevTools Profiler for detailed component analysis.');
}

// Auto-start monitoring
startMonitoring();
